'use client';

import React from 'react';

import ProtectedRoute from '@/components/auth/ProtectedRoute';
import Dashboard from '@/components/dashboard/Dashboard';
import { useOnboardingCompletionGuard } from '@/hooks/useNavigationGuard';

/**
 * Dashboard page - Protected route that requires authentication
 * Prevents back navigation after onboarding completion
 */
const DashboardPage: React.FC = () => {
  // Setup navigation guard to prevent back navigation after onboarding completion
  useOnboardingCompletionGuard();

  return (
    <ProtectedRoute requireAuth={true}>
      <Dashboard />
    </ProtectedRoute>
  );
};

export default DashboardPage;
