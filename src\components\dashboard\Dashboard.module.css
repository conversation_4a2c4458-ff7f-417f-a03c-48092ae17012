/* SealMyIdea Dashboard Styles */

.dashboardContent {
  min-height: 100vh;
}

.overviewSection {
  margin-bottom: 32px;
}

.sectionTitle {
  font-size: var(--font-xl) !important;
  font-weight: 600 !important;
  margin-bottom: 24px !important;
  color: var(--color-text-primary) !important;
}

/* Overview Cards */
.overviewCard {
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  background-color: var(--color-dashboard-card-background) !important;
}

.overviewCard:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
  border-color: var(--color-secondary) !important;
  background-color: var(--color-secondary) !important;
}

html[data-theme='light'] .overviewCard {
  border: 1px solid var(--color-border) !important;
}

html[data-theme='dark'] .overviewCard {
  border: 0px solid var(--color-border) !important;
}

html[data-theme='dark'] .overviewCard:hover {
  background-color: var(--color-secondary) !important;
}

/* Idea Cards */
.ideaCard {
  border-radius: 12px !important;
  border: 1px solid var(--color-border) !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  min-height: 200px !important;
}

.ideaCard:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
  border-color: var(--color-secondary) !important;
}

html[data-theme='light'] .ideaCard {
  border: 1px solid var(--color-border) !important;
}

html[data-theme='dark'] .ideaCard {
  border: 0px solid var(--color-border) !important;
}

html[data-theme='dark'] .ideaCard:hover {
  background-color: var(--color-secondary) !important;
  color: var(--color-primary);
}

/* Chart Cards */
.chartCard {
  border-radius: 12px !important;
  border: 1px solid var(--color-border) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

/* Icon Styling */
.iconContainer {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
}

/* Text Styling */
.cardTitle {
  font-size: var(--font-base) !important;
  font-weight: 600 !important;
  margin-bottom: 8px !important;
  color: var(--color-text-primary) !important;
}

.cardDescription {
  font-size: var(--font-sm) !important;
  color: var(--color-text-secondary) !important;
}

/* Status Labels */
.statusLabel {
  font-size: var(--font-xs) !important;
  font-weight: 500 !important;
}

.statusSealed {
  color: #52c41a !important;
}

.statusDraft {
  color: #1890ff !important;
}

/* Responsive Design */

/* Mobile Styles (xs: 0-575px) */
@media (max-width: 575px) {
  .dashboardContent {
    padding: 12px;
  }

  .sectionTitle {
    font-size: var(--font-lg) !important;
    margin-bottom: 16px !important;
  }

  .overviewSection {
    margin-bottom: 24px;
  }

  /* Adjust card padding for mobile */
  .overviewCard .ant-card-body,
  .ideaCard .ant-card-body {
    padding: 16px !important;
  }

  /* Reduce icon size on mobile */
  .iconContainer {
    width: 40px;
    height: 40px;
    margin: 0 auto 12px;
  }

  .iconContainer .anticon {
    font-size: 20px !important;
  }

  /* Adjust text sizes */
  .cardTitle {
    font-size: var(--font-sm) !important;
    margin-bottom: 6px !important;
  }

  .cardDescription {
    font-size: var(--font-xs) !important;
  }

  /* Chart height adjustment */
  .chartCard .ant-card-body > div {
    height: 250px !important;
  }
}

/* Tablet Styles (sm: 576-767px) */
@media (min-width: 576px) and (max-width: 767px) {
  .dashboardContent {
    padding: 16px;
  }

  .sectionTitle {
    font-size: var(--font-lg) !important;
    margin-bottom: 20px !important;
  }

  .overviewSection {
    margin-bottom: 28px;
  }
}

/* Medium Tablet Styles (md: 768-991px) */
@media (min-width: 768px) and (max-width: 991px) {
  .dashboardContent {
    /* padding: 20px; */
  }

  .sectionTitle {
    margin-bottom: 20px !important;
  }

  .overviewSection {
    margin-bottom: 28px;
  }
}

/* Additional responsive improvements */
@media (max-width: 575px) {
  /* Stack charts vertically on mobile */
  .chartCard .ant-card-body {
    padding: 12px !important;
  }

  /* Reduce section spacing on mobile */
  .overviewSection {
    margin-bottom: 20px;
  }

  /* Adjust My Ideas section spacing */
  .myIdeasSection {
    margin-top: 24px !important;
  }

  /* Adjust Document Activity section spacing */
  .documentActivitySection {
    margin-top: 24px !important;
  }
}

/* Ensure proper card spacing across all breakpoints */
.overviewCard,
.ideaCard {
  margin-bottom: 16px;
}

@media (min-width: 768px) {
  .overviewCard,
  .ideaCard {
    margin-bottom: 0;
  }
}
