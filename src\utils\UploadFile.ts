import {
  FileUpload<PERSON>ara<PERSON>,
  SASResponse,
  UploadProgress,
} from '@/components/uploadFiles/page';
import { Endpoints } from '@/config/endpoints';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import { NotificationInstance } from 'antd/es/notification/interface';
import { RcFile, UploadFile } from 'antd/es/upload';
import { Dispatch, SetStateAction } from 'react';
import { getChunkSize } from './CommonFunctions';

export const uploadProcessStart = async (
  data: SASResponse,
  originFile: UploadFile,
  controller: AbortController,
  file: UploadFile,
  updateFolderStep: () => void,
  setProgressMap: Dispatch<SetStateAction<Record<string, UploadProgress>>>,
  notification: NotificationInstance,
  isLast: boolean
) => {
  const actualFile = originFile.originFileObj as RcFile;
  if (!originFile?.size || !originFile.name) {
    return;
  }
  // const chunkSize = getChunkSize(originFile.size);
  // const totalChunks = Math.ceil((originFile.size || 0) / chunkSize);
  const chunkSize = getChunkSize(originFile.size);
  const totalChunks = Math.ceil(originFile.size / chunkSize);
  const blockIds: string[] = [];

  for (let i = 0; i < totalChunks; i++) {
    const start = i * chunkSize;
    const end = Math.min(originFile.size, start + chunkSize);
    const chunk = actualFile && actualFile?.slice(start, end);

    if (chunk.size === 0) {
      throw new Error(`Chunk ${i + 1} is empty.`);
    }

    const blockId = btoa(`block-${i.toString().padStart(6, '0')}`);
    blockIds.push(blockId);

    const chunkUrl = data.url.includes('?')
      ? `${data.url}&comp=block&blockid=${encodeURIComponent(blockId)}`
      : `${data.url}?comp=block&blockid=${encodeURIComponent(blockId)}`;

    const uploadRes = await fetch(chunkUrl, {
      method: 'PUT',
      headers: {
        'x-ms-blob-type': 'BlockBlob',
      },
      body: chunk,
      signal: controller.signal,
    });

    if (!uploadRes.ok) {
      const errText = await uploadRes.text();
      console.error(`Chunk ${i + 1} failed:`, errText);
      throw new Error(`Chunk ${i + 1} failed.`);
    }
    console.log('i  ===>', i, totalChunks);
    const res = await getApiData<FileUploadParams, ApiResponse>({
      url: Endpoints.fileSuccess,
      method: 'POST',
      data: {
        file_id: data.data._id,
        file_url: data.data.file_url,
        chunkIndex: i,
        status: i + 1 === totalChunks ? 'completed' : 'in_progress',
      },
    });

    if (res && res.status === true) {
      console.log('File upload success:', res.data);
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      setProgressMap((prev: any) => ({
        ...prev,
        [file.uid]: {
          percent: Math.round(((i + 1) / totalChunks) * 100),
          status: 'uploading',
        },
      }));
    } else {
      notification.error({
        message: res?.message || `File upload failed:`,
      });
      console.error('File upload success failed:', res?.message);
    }
  }

  const blockListXml =
    '<?xml version="1.0" encoding="utf-8"?>' +
    '<BlockList>' +
    blockIds.map(id => `<Latest>${id}</Latest>`).join('') +
    '</BlockList>';

  const commitUrl = data.url.includes('?')
    ? `${data.url}&comp=blocklist`
    : `${data.url}?comp=blocklist`;

  const commitRes = await fetch(commitUrl, {
    method: 'PUT',
    headers: {
      'x-ms-blob-content-type': originFile.type || '',
    },
    body: blockListXml,
    signal: controller.signal,
  });

  if (commitRes.ok) {
    if (isLast) {
      updateFolderStep();
    }
    setProgressMap(prev => ({
      ...prev,
      [file.uid]: { percent: 100, status: 'done' },
    }));
    notification.success({
      message: `${originFile.name} uploaded successfully.`,
    });
  } else {
    const errText = await commitRes.text();
    console.error('Commit block list failed:', errText);
    notification.error({
      message: `Failed to commit block list.`,
    });
    throw new Error('Failed to commit block list');
  }
};
