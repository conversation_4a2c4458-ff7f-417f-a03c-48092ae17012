'use client';

import AccountType from '../../../../../components/AccountType';

/**
 * AccountTyp eSettings page component
 *
 * @page AccountTypeSettings
 * @description Page for managing account type
 * @returns {JSX.Element} The AccountType Settings page component
 *
 * @example
 * // This page is automatically rendered when user navigates to /settings/account-type
 * // It displays the account information
 */
const AccountTypeSettings = () => {
  return <AccountType />;
};

export default AccountTypeSettings;
