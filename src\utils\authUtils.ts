/**
 * Authentication and Onboarding Utilities
 * Industry-standard authentication flow management
 */

import { User } from '@/store/slices/authSlice';

// Onboarding step definitions
export const ONBOARDING_STEPS = {
  EMAIL_VERIFICATION: 'email_verification',
  TWO_FA_SETUP: 'two_fa_setup',
  TWO_FA_VERIFICATION: 'two_fa_verification',
  STEP_1_TELL_ABOUT: 'screen_1_tell_about_us',
  STEP_2_GOALS: 'screen_2_goals',
  STEP_3_INTERESTS: 'screen_3_interest',
  STEP_4_HEAR_ABOUT: 'screen_4_here_about_us',
  TERMS_ACCEPTANCE: 'terms_acceptance',
  NOTIFICATION_PERMISSION: 'notification_permission',
  COMPLETED: 'completed',
} as const;

export type OnboardingStep = typeof ONBOARDING_STEPS[keyof typeof ONBOARDING_STEPS];

// Route definitions for onboarding flow
export const ONBOARDING_ROUTES = {
  [ONBOARDING_STEPS.EMAIL_VERIFICATION]: '/verify-otp',
  [ONBOARDING_STEPS.TWO_FA_SETUP]: '/two-factor-authentication',
  [ONBOARDING_STEPS.TWO_FA_VERIFICATION]: '/verify-otp',
  [ONBOARDING_STEPS.STEP_1_TELL_ABOUT]: '/steps?step=0',
  [ONBOARDING_STEPS.STEP_2_GOALS]: '/steps?step=1',
  [ONBOARDING_STEPS.STEP_3_INTERESTS]: '/steps?step=2',
  [ONBOARDING_STEPS.STEP_4_HEAR_ABOUT]: '/steps?step=3',
  [ONBOARDING_STEPS.TERMS_ACCEPTANCE]: '/terms',
  [ONBOARDING_STEPS.NOTIFICATION_PERMISSION]: '/notification-permission',
  [ONBOARDING_STEPS.COMPLETED]: '/dashboard',
} as const;

// Step order for progression tracking
export const STEP_ORDER: OnboardingStep[] = [
  ONBOARDING_STEPS.EMAIL_VERIFICATION,
  ONBOARDING_STEPS.TWO_FA_SETUP,
  ONBOARDING_STEPS.TWO_FA_VERIFICATION,
  ONBOARDING_STEPS.STEP_1_TELL_ABOUT,
  ONBOARDING_STEPS.STEP_2_GOALS,
  ONBOARDING_STEPS.STEP_3_INTERESTS,
  ONBOARDING_STEPS.STEP_4_HEAR_ABOUT,
  ONBOARDING_STEPS.TERMS_ACCEPTANCE,
  ONBOARDING_STEPS.NOTIFICATION_PERMISSION,
  ONBOARDING_STEPS.COMPLETED,
];

/**
 * Determines the current onboarding step based on user data
 */
export function getCurrentOnboardingStep(user: User | null): OnboardingStep {
  if (!user) {
    return ONBOARDING_STEPS.EMAIL_VERIFICATION;
  }

  // Check email verification
  if (!user.isEmailVerified) {
    return ONBOARDING_STEPS.EMAIL_VERIFICATION;
  }

  // Check 2FA setup and verification
  if (user.is2FAEnabled === '-1') {
    return ONBOARDING_STEPS.TWO_FA_SETUP;
  }
  
  if (user.is2FAEnabled === '1' && !user.last_screen) {
    return ONBOARDING_STEPS.TWO_FA_VERIFICATION;
  }

  // Check onboarding steps based on last_screen
  if (!user.last_screen) {
    return ONBOARDING_STEPS.STEP_1_TELL_ABOUT;
  }

  switch (user.last_screen) {
    case ONBOARDING_STEPS.STEP_1_TELL_ABOUT:
      return ONBOARDING_STEPS.STEP_2_GOALS;
    case ONBOARDING_STEPS.STEP_2_GOALS:
      return ONBOARDING_STEPS.STEP_3_INTERESTS;
    case ONBOARDING_STEPS.STEP_3_INTERESTS:
      return ONBOARDING_STEPS.STEP_4_HEAR_ABOUT;
    case ONBOARDING_STEPS.STEP_4_HEAR_ABOUT:
      return ONBOARDING_STEPS.TERMS_ACCEPTANCE;
    case ONBOARDING_STEPS.TERMS_ACCEPTANCE:
      return ONBOARDING_STEPS.NOTIFICATION_PERMISSION;
    case ONBOARDING_STEPS.COMPLETED:
      return ONBOARDING_STEPS.COMPLETED;
    default:
      return ONBOARDING_STEPS.STEP_1_TELL_ABOUT;
  }
}

/**
 * Checks if user has completed onboarding
 */
export function isOnboardingComplete(user: User | null): boolean {
  if (!user) return false;
  
  return user.last_screen === ONBOARDING_STEPS.COMPLETED || 
         user.is2FAEnabled === '0'; // Legacy check for users without 2FA
}

/**
 * Gets the route for the current onboarding step
 */
export function getOnboardingRoute(user: User | null, from?: string): string {
  const currentStep = getCurrentOnboardingStep(user);
  
  // Special handling for 2FA verification with context
  if (currentStep === ONBOARDING_STEPS.TWO_FA_VERIFICATION && user?.email) {
    return `/verify-otp?email=${encodeURIComponent(user.email)}&from=2FA`;
  }
  
  // Special handling for email verification with context
  if (currentStep === ONBOARDING_STEPS.EMAIL_VERIFICATION && user?.email) {
    const fromParam = from || 'login';
    return `/verify-otp?email=${encodeURIComponent(user.email)}&from=${fromParam}`;
  }

  return ONBOARDING_ROUTES[currentStep];
}

/**
 * Calculates onboarding progress percentage
 */
export function getOnboardingProgress(user: User | null): number {
  if (!user) return 0;
  
  const currentStep = getCurrentOnboardingStep(user);
  const currentIndex = STEP_ORDER.indexOf(currentStep);
  
  if (currentIndex === -1) return 0;
  
  return Math.round((currentIndex / (STEP_ORDER.length - 1)) * 100);
}

/**
 * Checks if a route should prevent back navigation
 */
export function shouldPreventBackNavigation(pathname: string, user: User | null): boolean {
  if (!user || !isOnboardingComplete(user)) {
    return false;
  }

  // Prevent back navigation from dashboard and protected routes after onboarding completion
  const protectedFromBack = [
    '/dashboard',
    '/my-ideas',
    '/settings',
    '/notification-permission'
  ];

  return protectedFromBack.some(route => pathname.startsWith(route));
}

/**
 * Gets the next step in onboarding flow
 */
export function getNextOnboardingStep(currentStep: OnboardingStep): OnboardingStep | null {
  const currentIndex = STEP_ORDER.indexOf(currentStep);
  
  if (currentIndex === -1 || currentIndex === STEP_ORDER.length - 1) {
    return null;
  }
  
  return STEP_ORDER[currentIndex + 1];
}

/**
 * Validates if user can access a specific route based on onboarding progress
 */
export function canAccessRoute(pathname: string, user: User | null): boolean {
  if (!user) return false;
  
  const currentStep = getCurrentOnboardingStep(user);
  const currentRoute = ONBOARDING_ROUTES[currentStep];
  
  // Allow access to current step and previous steps
  const allowedRoutes = STEP_ORDER
    .slice(0, STEP_ORDER.indexOf(currentStep) + 1)
    .map(step => ONBOARDING_ROUTES[step]);
  
  // Special cases for routes with query parameters
  if (pathname.startsWith('/verify-otp') || pathname.startsWith('/steps')) {
    return allowedRoutes.some(route => 
      route.startsWith('/verify-otp') || route.startsWith('/steps')
    );
  }
  
  return allowedRoutes.includes(pathname as any) || pathname === currentRoute;
}
