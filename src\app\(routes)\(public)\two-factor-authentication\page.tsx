'use client';

import type { InputRef } from 'antd';
import { Button, Input, Typography } from 'antd';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useRef, useState } from 'react';

import { LoginResponse } from '@/components/auth/AuthFunction';
import AuthWrapper, { authStyles } from '@/components/auth/AuthWrapper';
import { useNotification } from '@/components/Notification/NotificationContext';
import LoadingScreen from '@/components/ui/LoadingScreen';
import { Endpoints } from '@/config/endpoints';
import { images } from '@/config/images';
import { useTheme } from '@/contexts/ThemeContext';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  loginFailure,
  loginStart,
  loginSuccess,
} from '@/store/slices/authSlice';
import Image from 'next/image';
import { isMobile } from 'react-device-detect';

const { Title, Text } = Typography;

const TwoFactorAuthenticationPage: React.FC = () => {
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [loading, setLoading] = useState(false);
  const [btnLoading, setBtnLoading] = useState(false);
  const inputRefs = useRef<(InputRef | null)[]>([]);
  const router = useRouter();
  const dispatch = useAppDispatch();
  const notification = useNotification();
  const searchParams = useSearchParams();
  const { user } = useAppSelector(state => state.auth);

  const email = searchParams.get('email') || '';
  const [state, setState] = useState({
    qrCode: '',
    data: {
      qrCodeDataURL: '',
      secret: '',
    },
  });

  console.log('state', state);

  // const searchParams = useSearchParams();
  // const email = searchParams.get('email') || ''; // For future use

  const { t } = useTranslation();
  const { theme } = useTheme();

  const getQrCode = async () => {
    try {
      // Simulate API call
      setLoading(true);
      const res = await getApiData<{ email?: string }, ApiResponse>({
        url: Endpoints.enable2FA,
        method: 'POST',
        data: {
          email: email,
        },
      });

      if (res && res.status === true) {
        console.log('res', res);
        setState(p => ({ ...p, data: res.data }));
        notification.info({
          message: 'QR Code',
          description: res?.message || 'QR Code generated successfully',
        });
      }
      setLoading(false);
    } catch (error) {
      console.log('Error fetching QR code:', error);
    }
  };

  useEffect(() => {
    getQrCode();
    return () => {};
  }, []);

  const handleOTPChange = (index: number, value: string) => {
    if (value.length > 1) {
      return;
    }

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    if (value && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handleVerify = async () => {
    console.log('otp', otp);

    const otpCode = otp.join('');
    if (otpCode.length !== 6) {
      notification.error({
        message: 'QR Code',
        description: 'Please enter the complete 6-digit code',
      });
      return;
    }

    setBtnLoading(true);

    dispatch(loginStart());

    try {
      const res = await getApiData<
        { token: string; email: string },
        LoginResponse
      >({
        url: Endpoints.verify2FA,
        method: 'POST',
        data: {
          token: otpCode,
          email: email,
        },
      });

      // Simulate success (in real app, verify with backend)
      if (res && res?.status === true) {
        dispatch(
          loginSuccess({ ...user, ...res.data, isAuthenticated: false })
        );
        if (res.token) {
          localStorage.setItem('token', res?.token);
        }
        notification.success({
          message: 'QR Code',
          description: res?.message || '2FA successfully enabled.',
        });
        router.push('/steps?step=0');
      } else {
        notification.error({
          message: 'QR Code',
          description: res?.message || 'Verification failed. Please try again.',
        });
        dispatch(
          loginFailure(res?.message || 'Verification failed. Please try again.')
        );
      }
      setBtnLoading(false);
    } catch (error) {
      setBtnLoading(false);
      dispatch(
        loginFailure(error?.message || 'Verification failed. Please try again.')
      );
      notification.error({
        message: 'QR Code',
        description: error?.message || 'Verification failed. Please try again.',
      });
    }
  };

  const sm = useMediaQuery('(max-width: 900px)');
  const xs = useMediaQuery('(max-width: 600px)');

  return (
    <AuthWrapper>
      {loading ? (
        <LoadingScreen text={t('twoFactorAuthentication.loading')} />
      ) : (
        <>
          <div className={authStyles.logoSection}>
            <Image
              src={theme === 'dark' ? images.whiteLogo : images.horizontalLogo}
              alt='Swiss Trust Layer'
              className={authStyles.logoImage}
              width={180}
              height={180}
              style={{
                width: xs ? '175px' : sm ? '190px' : '220px',
                height: 'auto',
              }}
            />
            <Title level={2} className={authStyles.formTitle}>
              {t('twoFactorAuthentication.title')}
            </Title>
            {state.data?.qrCodeDataURL && (
              <img
                src={state.data?.qrCodeDataURL}
                alt='Swiss Trust Layer'
                className={authStyles.logoImage}
                width={150}
                height={150}
                style={{
                  width: isMobile ? '92px' : '150px',
                  height: 'auto',
                  marginTop: '20px',
                  marginBottom: '20px',
                }}
              />
            )}
            <Text className={authStyles.formSubtitle}>
              {t('twoFactorAuthentication.subTitle')}
            </Text>
          </div>

          <div className={authStyles.otpContainer}>
            {otp.map((digit, index) => (
              <Input
                key={index}
                ref={el => {
                  inputRefs.current[index] = el;
                }}
                autoFocus={index === 0}
                value={digit}
                onChange={e => {
                  const value = e.target.value;
                  // Only update if the value is a number or empty string
                  if (value === '' || /^\d+$/.test(value)) {
                    handleOTPChange(index, value);
                  }
                }}
                onKeyDown={e => handleKeyDown(index, e)}
                maxLength={1}
                placeholder='0'
                className={authStyles.otpInput}
                type='tel'
                pattern='[0-9]*'
                inputMode='numeric'
              />
            ))}
          </div>

          <div>
            <Button
              type='primary'
              onClick={handleVerify}
              loading={btnLoading}
              disabled={otp.join('').length !== 6}
              className={authStyles.primaryButton}
            >
              {t('twoFactorAuthentication.activate_account')}
            </Button>
          </div>
        </>
      )}
    </AuthWrapper>
  );
};

export default TwoFactorAuthenticationPage;
