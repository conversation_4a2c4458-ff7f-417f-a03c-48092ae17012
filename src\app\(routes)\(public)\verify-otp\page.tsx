'use client';

import type { InputRef } from 'antd';
import { Input, Typography } from 'antd';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { useNotification } from '@/components//Notification/NotificationContext';
import { handleRoute, LoginResponse } from '@/components/auth/AuthFunction';
import AuthWrapper, { authStyles } from '@/components/auth/AuthWrapper';
import { Endpoints } from '@/config/endpoints';
import { images } from '@/config/images';
import siteConfig from '@/config/site.config';
import { useTheme } from '@/contexts/ThemeContext';
import { getApiData } from '@/helpers/ApiHelper';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import {
  loginFailure,
  loginStart,
  loginSuccess,
  updateUser,
} from '@/store/slices/authSlice';
import Image from 'next/image';

interface user {
  _id: string;
  username: string;
  email: string;
  role: 'individual' | 'agency' | string;
  platform: 'web' | 'mobile' | string;
  isEmailVerified: boolean;
  mobile_number: string;
  country_code: string;
  status: '0' | '1' | string;
  notification_permission: boolean;
  createdAt: number;
  updatedAt: number;
}

interface VerifyOtpResponse {
  status: boolean;
  message: string;
  data?: user;
  token?: string;
  otp?: number;
}

const { Title, Text } = Typography;

const VerifyOTPPage: React.FC = () => {
  const dispatch = useAppDispatch();

  const { theme } = useTheme();
  const [otp, setOtp] = useState(['', '', '', '', '', '']);
  const [loading, setLoading] = useState(false);
  const { user } = useAppSelector(state => state.auth);
  const [resendLoading, setResendLoading] = useState(false);
  const [resendCooldown, setResendCooldown] = useState(0);
  const inputRefs = useRef<(InputRef | null)[]>([]);
  const router = useRouter();
  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';
  const from = searchParams.get('from') || '';
  const from2fa = from === '2FA';
  const notification = useNotification();
  const { t } = useTranslation();
  const RESEND_COOLDOWN_DURATION = 180; // in seconds
  useEffect(() => {
    if (!email) {
      return;
    }

    const storedTimestamp = localStorage.getItem('cooldown_start');
    const now = Date.now();

    if (storedTimestamp) {
      const diffInSeconds = Math.floor((now - Number(storedTimestamp)) / 1000);
      const remaining = RESEND_COOLDOWN_DURATION - diffInSeconds;
      if (remaining > 0) {
        setResendCooldown(remaining);
      } else {
        localStorage.removeItem('cooldown_start');
        setResendCooldown(0);
      }
    } else {
      localStorage.setItem('cooldown_start', now.toString());
      setResendCooldown(RESEND_COOLDOWN_DURATION);
    }
  }, [email]); // Only run when email changes (on mo unt)

  // Handle the countdown timer
  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (resendCooldown > 0) {
      interval = setInterval(() => {
        setResendCooldown(prev => {
          if (prev <= 1) {
            localStorage.removeItem(`cooldown_start_${email}`);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [resendCooldown]);

  const handleOTPChange = useCallback(
    (index: number, value: string) => {
      if (value.length > 1) {
        return;
      }

      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value && index < 5) {
        inputRefs.current[index + 1]?.focus();
      }

      if (value) {
        const isComplete = newOtp.every(digit => digit !== '');
        if (isComplete) {
          if (from2fa) {
            handle2FApi(newOtp.join(''));
          } else {
            handleVerifyWithOtp(newOtp.join(''));
          }
        }
      }
    },
    [from2fa, otp, from]
  );

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Modified verify function to accept OTP parameter
  const handleVerifyWithOtp = async (otpCode?: string) => {
    const finalOtpCode = otpCode || otp.join('');

    if (finalOtpCode.length !== 6) {
      notification.error({
        message: 'Verification failed.',
        description: 'Please enter the complete 6-digit code',
      });
      return;
    }

    setLoading(true);

    try {
      const response = await getApiData<
        { email: string; OTP: string; type: string },
        VerifyOtpResponse
      >({
        url: `${siteConfig.apiUrl}${Endpoints.verifyOtp}`,
        method: 'POST',
        data: {
          email,
          OTP: finalOtpCode,
          type:
            from === 'signup'
              ? 'signup_email_verification'
              : from === 'forgot_password'
                ? 'forgot_password_verification'
                : from === 'login'
                  ? 'signup_email_verification'
                  : '',
        },
        customUrl: true,
      });

      if (response && response.status === true) {
        if ('data' in response && response.data) {
          dispatch(updateUser({ user: { ...user, ...response.data } }));
        }
        if ('token' in response && response.token) {
          localStorage.setItem('token', response?.token);
        }
        if (from === 'forgot_password') {
          router.push(`/new-password?email=${encodeURIComponent(email)}`);
        }
        if (from === 'signup' || from === 'login') {
          const data = {
            ...response,
            data: {
              ...response.data,
              isEmailVerified: true,
              // Ensure we don't skip onboarding steps for new signups
              last_screen:
                from === 'signup' ? null : (response.data as any)?.last_screen,
            },
          };
          // router.push(`/steps`);
          // For manual redirection on 2FA Setup Screen as Email Verification is done
          handleRoute(data, router, from);
        }
      } else {
        notification.error({
          message: 'Verification failed.',
          description:
            response?.message || 'Invalid verification code. Please try again.',
        });
      }
    } catch {
      notification.error({
        message: 'Verification failed.',
        description: 'Verification failed. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  // Modified verify function to accept OTP parameter
  const handle2FApi = async (otpCode?: string) => {
    const finalOtpCode = otpCode || otp.join('');

    if (finalOtpCode.length !== 6) {
      notification.error({
        message: 'Verification failed.',
        description: 'Please enter the complete 6-digit code',
      });
      return;
    }
    dispatch(loginStart());
    setLoading(true);

    try {
      const response = await getApiData<
        { token: string; email: string },
        LoginResponse
      >({
        url: Endpoints.login2FA,
        method: 'POST',
        data: {
          token: finalOtpCode,
          email: email,
        },
      });

      if (response && response.status === true) {
        dispatch(loginSuccess({ ...user, ...response.data }));
        if (response.token) {
          localStorage.setItem('token', response?.token);
        }
        handleRoute(response, router, '2fa');
        // if ('data' in response && response.data) {
        //   dispatch(updateUser({ user: response.data }));
        // }
        // router.replace('/dashboard');
      } else {
        notification.error({
          message: 'Verification failed.',
          description:
            response?.message || 'Invalid verification code. Please try again.',
        });
        dispatch(
          loginFailure(
            response?.message || 'Verification failed. Please try again.'
          )
        );
      }
      setLoading(false);
    } catch (error) {
      dispatch(
        loginFailure(error?.message || 'Verification failed. Please try again.')
      );
      setLoading(false);
    }
  };
  // Keep the original handleVerify for manual button clicks
  const handleVerify = () => {
    if (from2fa) {
      handle2FApi();
    } else {
      handleVerifyWithOtp();
    }
  };

  const handleResend = async () => {
    setResendLoading(true);
    // Clear OTP input fields when resending
    setOtp(['', '', '', '', '', '']);

    try {
      const response = await getApiData<
        { email: string; type: string },
        VerifyOtpResponse
      >({
        url: `${siteConfig.apiUrl}${Endpoints.resendOtp}`,
        method: 'POST',
        data: {
          email,
          type:
            from === 'forgot_password'
              ? 'forgot_password_verification'
              : 'signup_email_verification',
        },
        customUrl: true,
      });
      if (response && response.status && 'otp' in response) {
        notification.success({
          message: 'OTP Sent',
          description: `Your OTP is ${response.otp}`,
        });
        setResendCooldown(180);
      } else {
        notification.error({
          message: 'Verification failed.',
          description:
            response?.message || 'Failed to resend code. Please try again.',
        });
      }
    } catch {
      notification.error({
        message: 'Verification failed.',
        description: 'Failed to resend code. Please try again.',
      });
    } finally {
      setResendLoading(false);
    }
  };

  const sm = useMediaQuery('(max-width: 900px)');
  const xs = useMediaQuery('(max-width: 600px)');

  return (
    <AuthWrapper>
      <div className={authStyles.logoSection}>
        <Image
          src={theme === 'dark' ? images.whiteLogo : images.horizontalLogo}
          alt='Swiss Trust Layer'
          className={authStyles.logoImage}
          width={180}
          height={180}
          style={{
            width: xs ? '175px' : sm ? '190px' : '220px',
            height: 'auto',
          }}
        />
        <Title level={2} className={authStyles.formTitle}>
          {t('verifyOtp.title')}
        </Title>
        <Text className={authStyles.formSubtitle}>
          {t(from2fa ? 'verifyOtp.2fa' : 'verifyOtp.subtitle')}
          <br />
          {from2fa ? '' : t('verifyOtp.subtitle1')}
        </Text>
      </div>

      <div className={authStyles.otpContainer}>
        {otp.map((digit, index) => (
          <Input
            key={index}
            ref={el => {
              inputRefs.current[index] = el;
            }}
            autoFocus={index === 0}
            value={digit}
            inputMode='numeric'
            onChange={e => handleOTPChange(index, e.target.value)}
            onKeyDown={e => handleKeyDown(index, e)}
            maxLength={1}
            pattern='[0-9]*'
            type='tel'
            placeholder='0'
            className={authStyles.otpInput}
          />
        ))}
      </div>

      {!from2fa && (
        <div className={authStyles.resendSection}>
          <div className={authStyles.resendText}>
            Didn&apos;t receive a code?
          </div>
          {resendCooldown > 0 ? (
            <span
              className={authStyles.resendLink}
              style={{ color: 'var(--text-secondary)' }}
            >
              {t('verifyOtp.resend_in')} {resendCooldown}s
            </span>
          ) : (
            <button
              className={authStyles.resendLink}
              onClick={handleResend}
              disabled={resendLoading}
              style={{ background: 'none', border: 'none', cursor: 'pointer' }}
            >
              {resendLoading
                ? `${t('verifyOtp.sending')}...`
                : `${t('verifyOtp.resend')}`}
            </button>
          )}
        </div>
      )}

      {/* <div className={authStyles.sendCodeButton}>
        <Button
          type='primary'
          htmlType='submit'
          onClick={handleVerify}
          loading={loading}
          disabled={otp.join('').length !== 6}
          className={authStyles.primaryButton}
        >
          {t('verifyOtp.verify')}
        </Button>
      </div> */}

      {/* <div className={authStyles.backToLoginLink}>
        <Link href='/login'>
          <ArrowLeftOutlined /> {t('verifyOtp.back_to_login')}
        </Link>
      </div> */}
    </AuthWrapper>
  );
};

export default VerifyOTPPage;
