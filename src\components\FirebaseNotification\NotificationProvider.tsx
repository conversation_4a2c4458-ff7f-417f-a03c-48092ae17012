'use client';

import { BellOutlined } from '@ant-design/icons';
import { notification, Typography } from 'antd';
import { onMessage } from 'firebase/messaging';
import React, { createContext, ReactNode, useContext, useEffect } from 'react';
import { messaging } from '../../notifications/firebase';
import { colors } from '../../theme/antdTheme';

const { Title, Text } = Typography;

interface NotificationPayload {
  notification?: {
    title?: string;
    body?: string;
  };
}

interface NotificationContextType {
  sendNotification: (payload: NotificationPayload) => void;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export const NotificationFirebaseProvider: React.FC<{
  children: ReactNode;
}> = ({ children }) => {
  const [api, contextHolder] = notification.useNotification();

  const sendNotification = (payload: NotificationPayload) => {
    playNotificationSound();
    api.success({
      message: (
        <Title
          style={{ color: colors.primary, fontSize: 20, fontWeight: 'bold' }}
        >
          {payload?.notification?.title}
        </Title>
      ),
      description: (
        <Text style={{ color: colors.light.textSecondary, fontSize: 16 }}>
          {payload?.notification?.body}
        </Text>
      ),
      icon: <BellOutlined style={{ color: colors.primary, fontSize: 26 }} />,
    });
  };
  const playNotificationSound = () => {
    const audio = new Audio('/notification.mp3'); // Put notification.mp3 in /public
    audio.play().catch(err => {
      console.warn(
        '🔇 Unable to play sound (autoplay blocked until user interacts):',
        err
      );
    });
  };
  useEffect(() => {
    if (!messaging) {
      return;
    }
    const unsubscribe = onMessage(messaging, payload => {
      console.log('📩 Foreground message received:', payload);
      sendNotification(payload as NotificationPayload);
    });

    return () => unsubscribe();
  }, []);

  return (
    <NotificationContext.Provider value={{ sendNotification }}>
      {contextHolder}
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotificationContext = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error(
      'useNotificationContext must be used within NotificationFirebaseProvider'
    );
  }
  return context;
};
