'use client';

import {
  BarChartOutlined,
  CloseOutlined,
  LogoutOutlined,
  SettingOutlined,
} from '@ant-design/icons';

import { Drawer, Modal, Space, Typography } from 'antd';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { useCallback, useMemo, useState } from 'react';

import { images } from '@/config/images';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { logout } from '@/store/slices/authSlice';

import { colors } from '@/theme/antdTheme';
import { BsPeopleFill } from 'react-icons/bs';
import { CiCirclePlus } from 'react-icons/ci';
import { FaDollarSign, FaFileAlt } from 'react-icons/fa';
import { IoIosNotifications } from 'react-icons/io';
import { RiDashboardFill } from 'react-icons/ri';
import styles from './Sidebar.module.css';

const { Title } = Typography;

const Sidebar = () => {
  const [visible, setVisible] = useState(false);
  const [logoutConfirmVisible, setLogoutConfirmVisible] = useState(false);

  const dispatch = useAppDispatch();
  const router = useRouter();
  const pathname = usePathname();

  const { theme } = useTheme();
  const { t } = useTranslation();

  const { user } = useAppSelector(state => state.auth);
  const userRole = user?.role || 'individual';

  const handleLogout = useCallback(() => {
    dispatch(logout());
    router.replace('/');
  }, [dispatch, router]);

  const menuItems = useMemo(
    () =>
      [
        {
          label: t('sidebar.dashboard'),
          icon: <RiDashboardFill size={18} />,
          path: '/dashboard',
          role: ['individual', 'agency'],
        },
        {
          label: t('sidebar.safeNewIdea'),
          icon: <CiCirclePlus size={18} />,
          path: '/safe-new-idea',
          className: 'safe-new-idea-item',
          role: ['individual', 'agency'],
        },
        {
          label: t('sidebar.myIdeas'),
          icon: <FaFileAlt size={18} />,
          path: '/my-ideas',
          role: ['individual', 'agency'],
        },
        {
          label: t('sidebar.sharingAndMember'),
          icon: <BsPeopleFill size={18} />,

          path: '/sharing',
          role: ['individual', 'agency'],
        },
        {
          label: t('sidebar.analytics'),
          icon: <BarChartOutlined />,
          path: '/analytics',
          role: ['agency'],
        },
        {
          label: t('sidebar.appearance_settings'),
          icon: <SettingOutlined />,
          path: '/appearance',
          role: ['agency'],
        },
        {
          label: t('sidebar.tenantManagement'),
          icon: <SettingOutlined />,
          path: '/tenant-management',
          role: ['agency'],
        },
        {
          label: t('sidebar.subscriptionAndBilling'),
          icon: <FaDollarSign size={18} />,
          path: '/billing',
          role: ['individual', 'agency'],
        },
        {
          label: t('sidebar.notification'),
          icon: <IoIosNotifications size={18} />,
          path: '/notification',
          role: ['individual', 'agency'],
        },
        {
          label: t('sidebar.analytics'),
          icon: <SettingOutlined />,
          path: '/analytics',
          role: ['agency'],
        },
      ].filter(item => item.role.includes(userRole)),
    [t, userRole]
  );

  const renderMenu = useCallback(
    () => (
      <div className={styles.sidebarContent}>
        <div className={styles.logo}>
          <Image
            src={theme === 'dark' ? images.whiteLogo : images.fullLogo}
            alt='Swiss Trust Layer'
            width={180}
            height={180}
            style={{ width: '150px', height: 'auto' }}
          />
        </div>
        <div className={styles.sidebarContent}>
          <ul className={styles.menu}>
            {menuItems.map(item => (
              <li
                key={item.label}
                className={`${styles.menuItem} ${
                  pathname.startsWith(item.path) ? styles.active : ''
                } ${item.className ? styles[item.className] : ''}`}
                onClick={() => {
                  router.push(item.path);
                  if (visible) {
                    setVisible(false);
                  }
                }}
              >
                {item.icon}
                <span className={styles.menuItemLabel}>{item.label}</span>
              </li>
            ))}
          </ul>
          <div
            className={styles.logout}
            onClick={() => setLogoutConfirmVisible(true)}
          >
            <LogoutOutlined /> <span>{t('sidebar.log_out')}</span>
          </div>
        </div>
      </div>
    ),
    [menuItems, pathname, router, t]
  );

  return (
    <>
      <div className='dashboard-sider'>
        {!visible && (
          <div className={styles.mobileToggle} onClick={() => setVisible(true)}>
            ☰
          </div>
        )}
        <div
          className={styles.sidebar}
          style={{ backgroundColor: colors[theme].drawerBackground }}
        >
          {renderMenu()}
        </div>
        <Drawer
          placement='left'
          closable
          onClose={() => setVisible(false)}
          open={visible}
          className={styles.drawer}
          style={{ backgroundColor: colors[theme].drawerBackground }}
          closeIcon={<CloseOutlined style={{ color: colors.primary }} />}
        >
          {renderMenu()}
        </Drawer>
      </div>

      <Modal
        title={null}
        open={logoutConfirmVisible}
        onCancel={() => {
          setLogoutConfirmVisible(false);
        }}
        centered
        closable={false}
        onOk={() => {
          handleLogout();
          setLogoutConfirmVisible(false);
        }}
        width={400}
      >
        <Space direction='vertical' style={{ width: '100%' }} size='large'>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center',
              textAlign: 'center',
            }}
          >
            <Title
              level={5}
              style={{
                color:
                  theme === 'dark'
                    ? 'var(--color-text-tertiary)'
                    : 'var(--color-text-primary)',
              }}
              className={styles.logoutTitle}
            >
              {t('sidebar.logoutPrompt')}
            </Title>
          </div>
        </Space>
      </Modal>
    </>
  );
};

export default Sidebar;
