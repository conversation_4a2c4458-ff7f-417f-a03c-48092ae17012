.container {
  max-width: 700px;
  margin: 0 auto;
  padding: 20px;
  text-align: center;
}

.heading {
  font-weight: 600;
}

.header {
  /* padding: 20px 24px 0; */
  text-align: start;
}

.backButton {
  padding: 0;
  margin: 0;
  font-size: 16px;
  color: var(--color-text-primary) !important;
  height: auto;
  padding-bottom: 10px;
}

.backButton:hover {
  color: var(--color-text-primary);
}

.card {
  background-color: var(--color-card);
  padding: 30px 20px;
  border-radius: 10px;
  margin: 40px 0px;
}

:global(html[data-theme='dark']) .card {
  background-color: rgb(77, 102, 138) !important;
}
:global(html[data-theme='light']) .card {
  background-color: #edeff1 !important;
}

.parent {
  width: 40% !important;
}

.iconWrapper {
  margin-bottom: 24px;
}

.infoGrid {
  text-align: left;
}

.infoRow {
  margin-bottom: 16px;
}

.label {
  font-weight: 500;
  font-size: 15px;
}

.value {
  text-align: right;
  font-size: 15px;
}

.active {
  color: #00c851;
  font-weight: 500;
  text-align: right;
  display: block;
}

.bottomButton {
  color: #fff;
  padding: 0 32px;
  min-width: 280px;
  max-width: 90%;
  margin-bottom: 15px;
}
.bottomButton:hover {
  background: var(--color-primaryHover) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(116, 150, 202, 0.3) !important;
  color: #fff !important;
}

/* Responsive */
@media screen and (max-width: 576px) {
  .card {
    padding: 20px 10px;
  }

  .label,
  .value {
    font-size: 14px;
  }

  .infoRow {
    margin-bottom: 12px;
  }

  .bottomButton {
    font-size: 14px;
  }
}
