.container {
  max-width: 70vw;
  margin: 0 auto;
  padding: 24px 0px 0px;
  font-family: 'Inter', sans-serif;
  background-color: var(--color-background);
}

.backButton {
  padding: 0;
  margin: 0;
  font-size: 16px;
  color: var(--color-text-primary) !important;
  height: auto;
  padding-bottom: 10px;
}

.backButton:hover {
  color: var(--color-text-primary);
}

.heading {
  font-weight: 700 !important;
  text-align: center;
  margin-bottom: 8px !important;
}

.subheading {
  text-align: center;
  display: block;
  margin-bottom: 24px;
  color: var(--color-text-primary) !important;
  font-size: 16px;
}

.searchWrapper {
  max-width: 400px;
  margin: 0 auto 32px;
}

.searchInput {
  height: 40px;
  border-radius: 8px;
}

.searchInput :global(.ant-input) {
  border-radius: 8px;
  border: 1px solid #d9d9d9;
  font-size: 14px;
}

.searchInput :global(.ant-input:focus),
.searchInput :global(.ant-input-focused) {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.searchInput :global(.ant-input-prefix) {
  color: #bfbfbf;
  margin-right: 8px;
}

.table {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.row {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr 1fr;
  align-items: start;
  padding-bottom: 16px;
  border-bottom: 1px solid #8f8888;
}

.row:last-child {
  border-bottom: none;
}

.headerRow {
  font-weight: 600;
  color: #595959;
  border-bottom: none;
  padding-bottom: 0;
}

.col {
  font-size: 14px;
  color: var(--color-text-primary);
  text-align: center;
}

.mobileLabel {
  display: none;
  font-weight: 600;
  color: #595959;
}
:global(html[data-theme='dark']) .mobileLabel {
  color: #c8c5c5;
}
:global(html[data-theme='light']) .mobileLabel {
  color: #595959;
}

@media (max-width: 768px) {
  .container {
    padding: 16px 12px;
  }

  .header {
    padding: 16px 20px 0;
  }

  .heading {
    font-size: 20px !important;
    margin-bottom: 6px !important;
  }

  .subheading {
    font-size: 14px;
    margin-bottom: 20px;
  }

  .searchWrapper {
    max-width: 100%;
    margin: 0 0 24px 0;
  }

  .searchInput {
    height: 44px;
    border-radius: 8px;
  }

  .searchInput :global(.ant-input) {
    font-size: 16px;
    padding: 8px 12px;
    bottom: 13px !important;
  }

  .searchInput :global(.ant-input-prefix) {
    font-size: 16px;
  }

  .row,
  .headerRow {
    grid-template-columns: 1fr;
    gap: 4px;
  }

  .headerRow {
    display: none;
  }

  .col {
    font-size: 14px;
    padding: 4px 0;
    text-align: start;
  }

  .row {
    border: 1px solid #f0f0f0;
    padding: 12px;
    border-radius: 8px;
  }

  .mobileLabel {
    display: inline;
  }

  .row:last-child {
    border-bottom: 1px solid #f0f0f0;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 12px 8px;
    max-width: 100vw;
  }

  .header {
    padding: 12px 16px 0;
  }

  .heading {
    font-size: 18px !important;
  }

  .subheading {
    font-size: 13px;
    margin-bottom: 16px;
  }

  .searchWrapper {
    margin: 0 0 20px 0;
  }

  .searchInput {
    height: 40px;
  }

  .searchInput :global(.ant-input) {
    font-size: 14px;
    padding: 6px 10px;
  }

  .searchInput :global(.ant-input-prefix) {
    font-size: 14px;
  }

  .col {
    font-size: 13px;
    text-align: start;
  }

  .row {
    padding: 10px;
  }

  .mobileLabel {
    display: inline;
  }

  .row:last-child {
    border-bottom: 1px solid #f0f0f0;
  }
}
