* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: var(--font-poppins) !important;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition:
    background-color 0.3s ease,
    color 0.3s ease;
}

/* styles/globals.css */
:root {
  /* Brand colors - consistent across themes */
  --color-primary: #012458;
  --color-secondary: #8edafe;
  --color-primaryHover: rgb(77, 102, 138);
  --color-primaryActive: #012458;
  --color-success: #52c41a;
  --color-success-hover: #73d13d;
  --color-warning: #faad14;
  --color-error: #ff4d4f;
  --color-info: #012458;
  --color-black: #000000;
  --color-white: #ffffff;

  /* Typography */
  --font-xs: 0.75rem;
  --font-sm: 0.875rem;
  --font-base: 1rem;
  --font-lg: 1.125rem;
  --font-xl: 1.25rem;
  --font-title: 2rem;
}

/* Light theme (default) */
html.light,
html[data-theme='light'] {
  --color-background: #ffffff;
  --color-background-secondary: #f5f7fa;
  --color-background-tertiary: #fafbfc;
  --color-text-primary: #333333;
  --color-text-secondary: #666666;
  --color-text-tertiary: #999999;
  --color-text-quaternary: #8c8c8c;
  --color-border: #e1e5e9;
  --color-border-secondary: #f0f0f0;
  --color-card: #edeff1;
  --color-card-active: #8edafe;
  --color-dashboard-card-background: #eefaff;
  --color-card-background: #ffffff;
  --color-selectable-item: #f5f5f5;

  --color-idea-card: var(--color-background);

  background-color: var(--color-background);
  color: var(--color-text-primary);
}

/* Dark theme */
html.dark,
html[data-theme='dark'] {
  --color-background: var(--color-primary);
  --color-background-secondary: var(--color-primary);
  --color-background-tertiary: #262626;
  --color-text-primary: #ffffff;
  --color-text-secondary: #d9d9d9;
  --color-text-tertiary: #8c8c8c;
  --color-text-quaternary: #595959;
  --color-border: #434343;
  --color-border-secondary: #303030;
  --color-card: #39547d;
  --color-card-active: #8edafe;
  --color-dashboard-card-background: var(--color-primary);
  --color-card-background: #1f1f1f;
  --color-selectable-item: #8c8c8c;

  --color-idea-card: #164071;

  background-color: var(--color-background);
  color: var(--color-text-primary);
}

a {
  color: inherit;
  text-decoration: none;
}

/* Smooth transitions for theme switching */
* {
  transition:
    background-color 0.3s ease,
    border-color 0.3s ease,
    color 0.3s ease;
}

/* Custom scrollbar for dark mode */
html.dark ::-webkit-scrollbar {
  width: 8px;
}

html.dark ::-webkit-scrollbar-track {
  background: #262626;
}

html.dark ::-webkit-scrollbar-thumb {
  background: #434343;
  border-radius: 4px;
}

html.dark ::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* Light mode scrollbar */
html.light ::-webkit-scrollbar {
  width: 8px;
}

html.light ::-webkit-scrollbar-track {
  background: #f1f1f1;
}

html.light ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

html.light ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.ant-form-item-explain-error {
  font-size: 13px !important;
  margin-top: 3px;
}

.ant-btn {
  font-size: 18px;
}

.ant-input {
  font-size: 16px;
}

.ant-input-outlined:focus-within {
  box-shadow: none !important;
}

.react-international-phone-input::placeholder {
  color: #bfbfbf;
  font-size: 16px;
}

/* Ant Design Input Placeholder Styles */
@media (max-width: 1200px) {
  .ant-btn {
    font-size: 16px;
    height: 45px;
  }
  .ant-input::placeholder {
    font-size: 16px;
  }
  .ant-input-password input::placeholder {
    font-size: 16px;
  }
  .react-international-phone-input::placeholder {
    font-size: 16px;
  }
}

@media (max-width: 900px) {
  .ant-btn {
    font-size: 14px;
    height: 45px;
  }
  .ant-input {
    font-size: 14px;
    height: 42px;
  }
  .ant-input::placeholder {
    font-size: 14px;
  }
  .ant-input-password input::placeholder {
    font-size: 14px;
  }
  .react-international-phone-input::placeholder {
    font-size: 14px;
  }
}

@media (max-width: 600px) {
  .ant-btn {
    font-size: 14px;
    height: 45px;
  }
  .ant-input {
    font-size: 14px;
    height: 42px;
  }
  .ant-input::placeholder {
    font-size: 14px;
  }
  .ant-input-password input::placeholder {
    font-size: 14px;
  }
  .react-international-phone-input::placeholder {
    font-size: 14px;
  }
}

.react-international-phone-country-selector {
  border-radius: 12px !important;
}

html.dark .react-international-phone-country-selector-dropdown {
  background-color: #06164a !important;
  color: #ffff;
}

html.dark .react-international-phone-country-selector-dropdown :hover {
  color: var(--color-primary);
}

/* Dark theme phone input styling */
html.dark .react-international-phone-input {
  background-color: #06164a !important;
  color: #fff !important;
  border-color: #06164a !important;
}

html.dark .react-international-phone-input::placeholder {
  color: #fff !important;
}

html.dark .react-international-phone-country-selector {
  background-color: #06164a !important;
  color: #fff !important;
  border-color: #06164a !important;
}

html.dark .react-international-phone-country-selector-dropdown {
  background-color: #06164a !important;
  color: #fff !important;
}

.ant-checkbox .ant-checkbox-inner {
  box-sizing: border-box;
  display: block;
  width: 22px;
  height: 22px;
  direction: ltr;
  background-color: #ffffff;
  border: 1px solid #555555;
  border-radius: 6px;
  border-collapse: separate;
  transition: all 0.3s;
}
.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #012458;
  border-color: #012458;
}

html.dark .ant-checkbox .ant-checkbox-inner {
  background-color: transparent;
  border: 1px solid #edeff1;
}

html.dark .ant-modal .ant-modal-content {
  background-color: #012458;
}

html.dark .ant-modal .ant-modal-close {
  color: #ffffff;
}
