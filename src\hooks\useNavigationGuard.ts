/**
 * Navigation Guard Hook
 * Manages browser history and prevents unwanted back navigation
 */

'use client';

import { useRouter, usePathname } from 'next/navigation';
import { useEffect, useRef } from 'react';

import { useAppSelector } from '@/store/hooks';
import { shouldPreventBackNavigation, isOnboardingComplete } from '@/utils/authUtils';

interface UseNavigationGuardOptions {
  /**
   * Whether to prevent back navigation
   */
  preventBack?: boolean;
  /**
   * Custom redirect path when back navigation is prevented
   */
  redirectTo?: string;
  /**
   * Callback when back navigation is attempted
   */
  onBackAttempt?: () => void;
}

/**
 * Hook to manage navigation guards and back button behavior
 */
export function useNavigationGuard(options: UseNavigationGuardOptions = {}) {
  const router = useRouter();
  const pathname = usePathname();
  const { user, isAuthenticated } = useAppSelector(state => state.auth);
  
  const {
    preventBack = false,
    redirectTo = '/dashboard',
    onBackAttempt
  } = options;

  const isInitialMount = useRef(true);
  const hasSetupGuard = useRef(false);

  useEffect(() => {
    // Auto-detect if back navigation should be prevented based on onboarding completion
    const shouldPrevent = preventBack || shouldPreventBackNavigation(pathname, user);
    
    if (!shouldPrevent || !isAuthenticated) {
      return;
    }

    // Only setup guard once per component mount
    if (hasSetupGuard.current) {
      return;
    }

    hasSetupGuard.current = true;

    // Add a dummy state to history to capture back navigation
    const setupHistoryGuard = () => {
      // Push current state to history
      window.history.pushState({ guardActive: true }, '', window.location.href);
      
      // Listen for popstate events (back/forward navigation)
      const handlePopState = (event: PopStateEvent) => {
        if (event.state?.guardActive) {
          // User tried to go back, prevent it
          window.history.pushState({ guardActive: true }, '', window.location.href);
          
          if (onBackAttempt) {
            onBackAttempt();
          } else {
            // Default behavior: redirect to dashboard
            router.replace(redirectTo);
          }
        }
      };

      window.addEventListener('popstate', handlePopState);

      // Cleanup function
      return () => {
        window.removeEventListener('popstate', handlePopState);
      };
    };

    // Setup guard after initial mount
    if (isInitialMount.current) {
      isInitialMount.current = false;
      // Small delay to ensure page is fully loaded
      const timer = setTimeout(setupHistoryGuard, 100);
      return () => clearTimeout(timer);
    } else {
      return setupHistoryGuard();
    }
  }, [pathname, user, isAuthenticated, preventBack, redirectTo, onBackAttempt, router]);

  return {
    /**
     * Manually trigger navigation guard setup
     */
    setupGuard: () => {
      hasSetupGuard.current = false;
    },
    
    /**
     * Check if back navigation is currently prevented
     */
    isBackPrevented: preventBack || shouldPreventBackNavigation(pathname, user),
    
    /**
     * Navigate with guard consideration
     */
    navigateWithGuard: (path: string, options?: { replace?: boolean }) => {
      if (options?.replace) {
        router.replace(path);
      } else {
        router.push(path);
      }
    }
  };
}

/**
 * Hook specifically for onboarding completion guard
 * Automatically prevents back navigation after onboarding is complete
 */
export function useOnboardingCompletionGuard() {
  const { user } = useAppSelector(state => state.auth);
  const pathname = usePathname();
  
  return useNavigationGuard({
    preventBack: isOnboardingComplete(user) && pathname.startsWith('/dashboard'),
    redirectTo: '/dashboard',
    onBackAttempt: () => {
      console.log('Back navigation prevented: Onboarding completed');
    }
  });
}

/**
 * Hook for protecting specific routes from back navigation
 */
export function useRouteProtection(protectedPaths: string[], redirectTo = '/dashboard') {
  const pathname = usePathname();
  
  const isProtectedRoute = protectedPaths.some(path => pathname.startsWith(path));
  
  return useNavigationGuard({
    preventBack: isProtectedRoute,
    redirectTo,
    onBackAttempt: () => {
      console.log(`Back navigation prevented from protected route: ${pathname}`);
    }
  });
}

/**
 * Utility function to clear navigation history
 * Useful when transitioning between major app sections
 */
export function clearNavigationHistory() {
  if (typeof window !== 'undefined') {
    // Replace current history entry to prevent going back
    window.history.replaceState(null, '', window.location.href);
  }
}

/**
 * Utility function to setup one-way navigation
 * Prevents going back to previous route
 */
export function setupOneWayNavigation(router: ReturnType<typeof useRouter>, targetPath: string) {
  // Navigate to target and replace history
  router.replace(targetPath);
  
  // Clear any previous history entries
  setTimeout(() => {
    clearNavigationHistory();
  }, 100);
}
