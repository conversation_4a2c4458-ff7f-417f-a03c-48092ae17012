'use client';

import { useTranslation } from '@/hooks/useTranslation';
import { ArrowLeftOutlined, SearchOutlined } from '@ant-design/icons';
import { Button, Input, Typography } from 'antd';
import { useRouter } from 'next/navigation';
import styles from './accountHistory.module.css';
const { Title, Text } = Typography;

export default function AccountHistory() {
  const router = useRouter();
  const { t } = useTranslation();

  const dummyData = Array(6).fill({
    date: 'Jul 13, 2025 11:02 AM',
    action: t('accountHistory.sealed_document'),
    description: t('accountHistory.document_was_sealed'),
    user: t('accountHistory.you'),
  });

  return (
    <div className={styles.container}>
      <Button
        type='link'
        icon={<ArrowLeftOutlined />}
        className={styles.backButton}
        onClick={() => {
          router.replace('/settings');
        }}
      >
        {t('common.back')}
      </Button>

      <Title level={2} className={styles.heading}>
        {t('settings.activity_history')}
      </Title>
      <Text className={styles.subheading}>
        {t('activity_history.track_your_account')}
      </Text>

      <div className={styles.searchWrapper}>
        <Input
          placeholder={`${t('activity_history.search_activity')}...`}
          prefix={<SearchOutlined />}
          className={styles.searchInput}
        />
      </div>

      <div className={styles.table}>
        <div className={`${styles.row} ${styles.headerRow}`}>
          <Text className={styles.col}>{t('accountHistory.date_time')}</Text>
          <Text className={styles.col}>{t('accountHistory.action')}</Text>
          <Text className={styles.col}>{t('accountHistory.description')}</Text>
          <Text className={styles.col}>{t('accountHistory.by_user')}</Text>
        </div>

        {dummyData.map((item, index) => (
          <div key={index} className={styles.row}>
            <Text className={styles.col}>
              <span className={styles.mobileLabel}>
                {t('accountHistory.date_time')}:{' '}
              </span>
              {item.date}
            </Text>
            <Text className={styles.col}>
              <span className={styles.mobileLabel}>
                {t('accountHistory.action')}:{' '}
              </span>
              {item.action}
            </Text>
            <Text className={styles.col}>
              <span className={styles.mobileLabel}>
                {t('accountHistory.description')}:{' '}
              </span>
              {item.description}
            </Text>
            <Text className={styles.col}>
              <span className={styles.mobileLabel}>
                {t('accountHistory.by_user')}:{' '}
              </span>
              {item.user}
            </Text>
          </div>
        ))}
      </div>
    </div>
  );
}
