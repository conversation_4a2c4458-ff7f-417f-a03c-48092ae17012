/* MyIdeasScreen CSS Module */

/* Mobile Styles (xs: 0-575px) */
@media (max-width: 575px) {
  .container {
    padding: 12px;
  }
}
.header {
  margin-bottom: 32px;
}

.pageTitle {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: var(--color-text-primary) !important;
  margin-bottom: 24px !important;
}

.searchAndFilters {
  margin-bottom: 32px;
}

.filterButtons {
  margin-top: 16px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.filterButton {
  border-radius: 8px !important;
  height: 45px !important;
  border: 1px solid var(--color-border) !important;
  background-color: var(--color-background) !important;
  color: var(--color-text-secondary) !important;
  font-family: var(--font-poppins) !important;
  font-weight: 300 !important;
  font-size: 14px !important;
}

.filterButton:hover {
  background-color: var(--color-secondary) !important;
  border-color: var(--color-secondary) !important;
  color: var(--color-primary) !important;
}

.filterButtonActive {
  background-color: var(--color-secondary) !important;
  border-color: var(--color-secondary) !important;
  color: var(--color-primary) !important;
  font-family: var(--font-poppins) !important;
  font-weight: 400 !important;
  font-size: 14px !important;
}

.filterButtonActive:hover {
  background-color: var(--color-secondary) !important;
  border-color: var(--color-secondary) !important;
  color: var(--color-primary) !important;
}

.foldersGrid .ant-row {
  margin-bottom: 24px;
}

.foldersSpinner {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.loadMoreWrapper {
  text-align: center;
  margin: 24px auto;
}

@media (max-width: 1200px) {
  .pageTitle {
    font-size: 30px !important;
  }

  .filterButtons {
    gap: 8px !important;
  }

  .filterButton {
    height: 40px !important;
    font-size: 14px !important;
  }
}
@media (max-width: 900px) {
  .pageTitle {
    font-size: 26px !important;
  }
  .searchInput :global(.ant-input-prefix) {
    margin-right: 8px;
    color: var(--text-secondary);
  }

  .searchInput {
    font-size: 14px;
    height: 42px;
    padding: 0px 15px;
  }
}

@media (max-width: 625px) {
  .pageTitle {
    font-size: 24px !important;
  }
  .container {
    padding: 12px;
  }
}
