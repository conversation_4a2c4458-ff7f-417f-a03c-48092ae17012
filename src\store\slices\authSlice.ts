/**
 * @fileoverview Authentication Redux slice for managing user state and authentication
 * @module store/slices/authSlice
 * @description Handles user authentication state, login/logout actions, and user profile management
 */

import { createSlice, PayloadAction } from '@reduxjs/toolkit';

/**
 * User interface representing the authenticated user's data
 * @interface User
 * @property {string} [username] - User's unique username
 * @property {string} [email] - User's email address
 * @property {string} [role] - User's role in the system
 * @property {string} [birth_date] - User's birth date
 * @property {string} [gender] - User's gender
 * @property {string} [purpose_of_joining] - Reason for joining the platform
 * @property {string} [fullname] - User's full name
 * @property {string} [platform] - Platform used for registration
 * @property {boolean} [isEmailVerified] - Email verification status
 * @property {string | number} [mobile_number] - User's mobile phone number
 * @property {string} [country_code] - Country code for mobile number
 * @property {string | number} [status] - User account status
 * @property {string[]} [goals] - User's selected goals
 * @property {string[]} [interests] - User's interests
 * @property {string} [hear_about_us] - How user heard about the platform
 * @property {boolean} [notification_permission] - Notification permission status
 * @property {string | null} [last_screen] - Last visited screen/page
 * @property {number | string} [createdAt] - Account creation timestamp
 * @property {number | string} [updatedAt] - Last update timestamp
 * @property {string} [profile_pic] - Last update profile pic
 */
export interface User {
  _id?: string;
  username?: string;
  email?: string;
  role?: string;
  birth_date?: string;
  gender?: string;
  purpose_of_joining?: string;
  fullname?: string;
  platform?: string;
  isEmailVerified?: boolean;
  is2FAEnabled?: boolean | string | null;
  mobile_number?: string | number;
  country_code?: string;
  status?: string | number;
  goals?: string[];
  interests?: string[];
  hear_about_us?: string;
  notification_permission?: boolean;
  last_screen?: string | null | undefined;
  createdAt?: number | string;
  updatedAt?: number | string;
  profile_pic?: string;
  isAuthenticated?: boolean;
}

/**
 * Authentication state interface
 * @interface AuthState
 * @property {boolean} isAuthenticated - Whether user is currently authenticated
 * @property {User | null} user - Current user data or null if not authenticated
 * @property {boolean} loading - Loading state for authentication operations
 * @property {string | null} error - Error message if authentication fails
 */
interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

/**
 * Payload interface for updating user data
 * @interface UpdateUserPayload
 * @property {Partial<User>} user - Partial user data to update
 * @property {boolean} [isAuthenticated] - Optional authentication status update
 */
interface UpdateUserPayload {
  user: Partial<User>;
  isAuthenticated?: boolean;
}

/**
 * Retrieves initial authentication state from localStorage with fallback
 * This function safely handles localStorage access for SSR compatibility
 *
 * @function getInitialAuthState
 * @returns {AuthState} Initial authentication state
 * @description
 * - Attempts to restore auth state from localStorage
 * - Falls back to default unauthenticated state if localStorage is unavailable
 * - Handles JSON parsing errors gracefully
 */
const getInitialAuthState = (): AuthState => {
  if (typeof window !== 'undefined') {
    try {
      const savedAuth = localStorage.getItem('auth-state');
      if (savedAuth) {
        const parsed = JSON.parse(savedAuth);
        return {
          isAuthenticated: parsed.isAuthenticated || false,
          user: parsed.user || null,
          loading: false,
          error: null,
        };
      }
    } catch {
      // Ignore localStorage errors
    }
  }

  return {
    isAuthenticated: false,
    user: null,
    loading: false,
    error: null,
  };
};

/**
 * Initial authentication state
 * @constant {AuthState}
 */
const initialState: AuthState = getInitialAuthState();

/**
 * Authentication Redux slice containing all auth-related actions and reducers
 *
 * @constant {Object} authSlice
 * @description
 * This slice manages the complete authentication lifecycle including:
 * - Login/logout operations with loading states
 * - User profile updates
 * - Role management
 * - Persistent storage integration
 * - Error handling
 *
 * All actions automatically handle localStorage persistence and cookie management
 * for seamless authentication across browser sessions.
 */
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    /**
     * Initiates login process and sets loading state
     * @function loginStart
     * @param {AuthState} state - Current auth state
     */
    loginStart: state => {
      state.loading = true;
      state.error = null;
    },

    /**
     * Handles successful login with user data persistence
     * @function loginSuccess
     * @param {AuthState} state - Current auth state
     * @param {PayloadAction<User>} action - Action containing user data
     */
    loginSuccess: (state, action: PayloadAction<User>) => {
      console.log('loginSuccess', action.payload);
      state.isAuthenticated = action.payload?.isAuthenticated || false;
      state.user = action.payload;
      state.loading = false;
      state.error = null;

      // Persist to localStorage and set cookie
      if (typeof window !== 'undefined') {
        const authState = {
          isAuthenticated: action.payload?.isAuthenticated || false,
          user: action.payload,
        };
        localStorage.setItem('auth-state', JSON.stringify(authState));

        // Set cookie for middleware
        document.cookie = `auth-token=authenticated; path=/; max-age=${60 * 60 * 24 * 7}`; // 7 days
      }
    },

    /**
     * Handles login failure with error message
     * @function loginFailure
     * @param {AuthState} state - Current auth state
     * @param {PayloadAction<string>} action - Action containing error message
     */
    loginFailure: (state, action: PayloadAction<string>) => {
      state.loading = false;
      state.error = action.payload;
    },
    setRole: (state, action: PayloadAction<string>) => {
      if (state.user) {
        state.user.role = action.payload;
      } else {
        // Store role temporarily for unauthenticated users
        state.user = {
          username: '',
          role: action.payload,
          birth_date: '',
          gender: '',
          purpose_of_joining: '',
          hear_about_us: '',
        };
      }

      // Update localStorage
      if (typeof window !== 'undefined') {
        const authState = {
          isAuthenticated: state.isAuthenticated,
          user: state.user,
        };
        localStorage.setItem('auth-state', JSON.stringify(authState));
      }
    },
    setPreAuthRole: (state, action: PayloadAction<string>) => {
      // Store role for user selection before login
      if (!state.user) {
        state.user = {
          username: '',
          role: action.payload,
          birth_date: '',
          gender: '',
          purpose_of_joining: '',
          hear_about_us: '',
        };
      } else {
        state.user.role = action.payload;
      }

      if (typeof window !== 'undefined') {
        localStorage.setItem('selected-role', action.payload);
      }
    },
    logout: state => {
      state.isAuthenticated = false;
      state.user = null;
      state.loading = false;
      state.error = null;

      // Clear localStorage and cookie
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth-state');
        localStorage.removeItem('token');
        // Clear cookie
        document.cookie =
          'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
      }
    },
    updateUser: (state, action: PayloadAction<UpdateUserPayload>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload.user };
      } else {
        state.user = { ...action.payload.user } as User;
      }
      if (typeof action.payload.isAuthenticated === 'boolean') {
        state.isAuthenticated = action.payload.isAuthenticated;
      }
      // Update localStorage
      if (typeof window !== 'undefined') {
        // localStorage.setItem('auth-state', JSON.stringify(authState));
      }
    },
  },
});

export const {
  loginStart,
  loginSuccess,
  loginFailure,
  setRole,
  setPreAuthRole,
  logout,
  updateUser,
} = authSlice.actions;
export default authSlice.reducer;
