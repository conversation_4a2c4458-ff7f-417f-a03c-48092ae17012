import { images } from '@/config/images';
import { useTranslation } from '@/hooks/useTranslation';
import { downloadFile } from '@/utils/CommonFunctions';
import { Button, Divider, Typography } from 'antd';
import dayjs from 'dayjs';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { isMobile } from 'react-device-detect';
import { PaymentStateProps } from '../PaymentStatus/PaymentStatus';
import styles from './PaymentReceipt.module.css';

const { Title, Text } = Typography;

const PaymentReceipt = ({
  type = 'success',
  data,
}: {
  type: 'success' | 'failed';
  data: PaymentStateProps;
}) => {
  const router = useRouter();
  const { t } = useTranslation();

  // const handleDownloadReciept = async () => {
  //   try {
  //     const res = await getApiData<{ transaction_id: string }, ApiResponse>({
  //       url: `${Endpoints.downloadReceipt}?transaction_id=${data?.transaction_id}`,
  //       method: 'GET',
  //       data: {
  //         transaction_id: data?.transaction_id,
  //       },
  //     }); // your GET API
  //     if (res?.status === true && res?.data) {
  //       window.open(res?.data?.url, '_blank');
  //     } else {
  //       notification.error({
  //         message: res?.message || 'Error downloading receipt',
  //       });
  //     }
  //   } catch (error) {
  //     notification.error({
  //       message: 'Download Failed',
  //       description: 'Failed to download the receipt. Please try again.',
  //     });
  //     console.error('Download failed:', error);
  //   }
  // };

  return (
    <div className={styles.container}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
          width: '100%',
          justifyContent: 'center',
        }}
      >
        <Title level={1} className={styles.pageTitle}>
          {t('receipt.title')}
        </Title>
      </div>

      <div className={styles.content}>
        <div className={styles.iconSection}>
          <Image
            src={images.receipt}
            alt='Receipt'
            width={isMobile ? 60 : 100}
            height={isMobile ? 90 : 150}
          />
        </div>
        <div className={styles.textSection}>
          <div className={styles.propSection}>
            <Text className={styles.propTitle}>{t('receipt.date')}</Text>
            <Text className={styles.propText}>
              {dayjs(data?.issueDate).format('MMM DD, YYYY')}
            </Text>
          </div>
          <Divider className={styles.divider} />
          <div className={styles.propSection}>
            <Text className={styles.propTitle}>{t('receipt.amount')}</Text>
            <Text className={styles.propText}>${data?.total}</Text>
          </div>
          <Divider className={styles.divider} />
          <div className={styles.propSection}>
            <Text className={styles.propTitle}>{t('receipt.plan')}</Text>
            <Text className={styles.propText}>{data?.planName || '-'}</Text>
          </div>
          <Divider className={styles.divider} />
          <div className={styles.propSection}>
            <Text className={styles.propTitle}>{t('receipt.invoice')}</Text>
            <Text className={styles.propText}>{data?.invoiceId || '-'}</Text>
          </div>
          <Divider className={styles.divider} />
          <div className={styles.propSection}>
            <Text className={styles.propTitle}>{t('receipt.expired_on')}</Text>
            <Text className={styles.propText}>
              {data?.planExpiredOn || '-'}
            </Text>
          </div>
        </div>
      </div>
      <div className={styles.buttonSection}>
        <Button
          type='primary'
          style={{ width: '100%' }}
          onClick={() => {
            if (data?.receiptUrl) {
              downloadFile(data.receiptUrl);
            }
          }}
        >
          {t('receipt.download_pdf')}
        </Button>
        <Button
          type='default'
          style={{ width: '100%' }}
          onClick={() => {
            router.replace('/my-ideas');
          }}
        >
          {t('receipt.back_to_idea')}
        </Button>
      </div>
    </div>
  );
};

export default PaymentReceipt;
