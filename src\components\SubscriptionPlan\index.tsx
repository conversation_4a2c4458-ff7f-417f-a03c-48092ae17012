import { ArrowLeftOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Col, Row } from 'antd';
import { useRouter } from 'next/navigation';
import { useTranslation } from '../../hooks/useTranslation';
import styles from './subscriptionPlan.module.css';

const SubscriptionPlans = () => {
  const router = useRouter();
  const { t } = useTranslation();

  const plans = [
    {
      title: t('subscriptionPlan.basic'),
      price: '₹0.00',
      description: t('subscriptionPlan.basic_description'),
    },
    {
      title: t('subscriptionPlan.professional'),
      price: '₹10',
      description: t('subscriptionPlan.professional_description_1'),
    },
    {
      title: t('subscriptionPlan.professional'),
      price: '₹30',
      description: t('subscriptionPlan.professional_description_2'),
    },
    {
      title: t('subscriptionPlan.professional'),
      price: '₹100',
      description: t('subscriptionPlan.professional_description_3'),
    },
  ];

  return (
    <div className={styles.main}>
      <div className={styles.container}>
        <div className={styles.header}>
          <Button
            type='link'
            icon={<ArrowLeftOutlined />}
            className={styles.backButton}
            onClick={() => {
              router.replace('/settings');
            }}
          >
            {t('common.back')}
          </Button>
        </div>
        <h2 className={styles.heading}>{t('subscriptionPlan.title')}</h2>
        <Row gutter={[24, 24]} justify='center' className={styles.parent}>
          {plans.map((plan, index) => (
            <Col key={index} xs={24} sm={24} md={12} lg={12} xl={12}>
              <Card
                className={styles.card}
                onClick={() => {
                  router.replace('/settings/subscription');
                }}
              >
                <p className={styles.title}>{plan.title}</p>
                <p className={styles.price}>
                  {plan.price}{' '}
                  <p className={styles.month}>
                    {t('subscriptionPlan.per_month')}
                  </p>
                </p>
                <p className={styles.description}>{plan.description}</p>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    </div>
  );
};

export default SubscriptionPlans;
