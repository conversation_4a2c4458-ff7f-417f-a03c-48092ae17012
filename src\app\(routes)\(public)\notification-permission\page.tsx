'use client';

import React from 'react';

import PublicRoute from '@/components/auth/PublicRoute';
import { useNavigationGuard } from '@/hooks/useNavigationGuard';
import NotificationPermission from '../../../../components/NotificationPermission/notificationPermission';

const NotificationPermissionPage: React.FC = () => {
  // Prevent back navigation during final onboarding step
  useNavigationGuard({
    preventBack: true,
    redirectTo: '/notification-permission',
    onBackAttempt: () => {
      console.log('Back navigation prevented: Final onboarding step');
    },
  });

  return (
    <PublicRoute>
      <NotificationPermission />
    </PublicRoute>
  );
};

export default NotificationPermissionPage;
