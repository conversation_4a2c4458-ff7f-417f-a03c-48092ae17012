'use client';

import { ArrowLeftOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Col, Row, Spin, Typography } from 'antd';
import dayjs from 'dayjs';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Endpoints } from '../../config/endpoints';
import { getApiData } from '../../helpers/ApiHelper';
import { useTranslation } from '../../hooks/useTranslation';
import { useAppSelector } from '../../store/hooks';
import styles from './accountType.module.css';

const { Title, Text } = Typography;

interface UserInfo {
  role: string;
  createdAt: number;
  updatedAt: number;
}
interface ApiResponse<T> {
  status: boolean;
  data: T;
  message: string;
}

const AccountType = () => {
  const router = useRouter();
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const { user } = useAppSelector(s => s.auth);
  const [userInfo, setUserInfo] = useState<UserInfo | null>(null);

  useEffect(() => {
    getAccountDetails();
  }, []);

  const getAccountDetails = async () => {
    setLoading(true);
    try {
      // eslint-disable-next-line @typescript-eslint/no-empty-object-type
      const res = await getApiData<{}, ApiResponse<UserInfo>>({
        url: `${Endpoints.userAccountDetails}${user?._id}`,
        method: 'GET',
      });
      if (res && res.status === true && res.data) {
        setUserInfo(res?.data);
      }
    } catch (error) {
      console.error('Error fetching folder data:', error);
    } finally {
      setLoading(false);
    }
  };

  const plans = [
    {
      title: t('accountType.basic'),
      price: '$0',
      description: t('accountType.basic_description'),
      highlight: true,
    },
    {
      title: t('accountType.starter'),
      price: '$10',
      description: t('accountType.starter_description'),
    },
    {
      title: t('accountType.professional'),
      price: '$30',
      description: t('accountType.professional_description'),
    },
    {
      title: t('accountType.enterprise'),
      price: '$100',
      description: t('accountType.enterprise_description'),
    },
  ];

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.content}>
          <div className={styles.header}>
            <Button
              type='link'
              icon={<ArrowLeftOutlined />}
              className={styles.backButton}
              onClick={() => {
                router.replace('/settings');
              }}
            >
              {t('common.back')}
            </Button>
          </div>
          <Title level={2} className={styles.title}>
            {t('accountType.title')}
          </Title>
          <div className={styles.foldersSpinner}>
            <Spin size='default' />
            <Text className={styles.loadingText}>{t('common.loading')}</Text>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.content}>
        {' '}
        <div className={styles.header}>
          <Button
            type='link'
            icon={<ArrowLeftOutlined />}
            className={styles.backButton}
            onClick={() => {
              router.replace('/settings');
            }}
          >
            {t('common.back')}
          </Button>
        </div>
        <Title level={2} className={styles.title}>
          {t('accountType.title')}
        </Title>
        <Text className={styles.subtitle}>{t('accountType.subtitle')}</Text>
        <div className={styles.accountInfoCard}>
          <div className={styles.infoRow}>
            <Text strong>{t('accountType.account_type_label')}</Text>
            <Text>
              {userInfo?.role
                ? userInfo.role.charAt(0).toUpperCase() + userInfo.role.slice(1)
                : '-'}{' '}
              User
            </Text>
          </div>
          <div className={styles.infoRow}>
            <Text strong>{t('accountType.member_since_label')}</Text>
            <Text>
              {userInfo?.createdAt
                ? dayjs(userInfo.createdAt).format('MMMM YYYY')
                : '-'}
            </Text>
          </div>
          <div className={styles.infoRow}>
            <Text strong>{t('accountType.plan_label')}</Text>
            <Text>{t('accountType.basic_free_tier')}</Text>
          </div>
        </div>
        <Title level={3} className={styles.sectionTitle}>
          {t('accountType.usage_and_upgrade')}
        </Title>
        <Row gutter={[24, 24]} className={styles.mainSection}>
          <Col xs={24} sm={24} md={8} lg={8}>
            <div className={styles.usageSection}>
              <Text className={styles.usageLabel}>
                {t('accountType.usage')}
              </Text>
              <div className={styles.usageProgress}>
                <div className={styles.percentageText}>75%</div>
                <div className={styles.fill}>
                  <Text className={styles.usedText}>7.5 Mb</Text>
                </div>
              </div>
            </div>
          </Col>

          <Col xs={24} sm={24} md={16} lg={16}>
            <div className={styles.plansSection}>
              <Row gutter={[16, 16]}>
                {plans.map(plan => (
                  <Col xs={24} sm={24} md={12} lg={12} key={plan.title}>
                    <Card
                      className={`${styles.planCard} ${plan.highlight ? styles.highlightCard : ''}`}
                    >
                      <Text
                        className={`${styles.planTitle} ${plan.highlight ? styles.planTitleHightlight : ''}`}
                      >
                        {plan.title}
                      </Text>
                      <Title
                        level={3}
                        className={`${styles.price} ${plan.highlight ? styles.priceHightlight : ''}`}
                      >
                        {plan.price}
                        <span
                          className={`${styles.perMonth} ${plan.highlight ? styles.perMonthHightlighted : ''}`}
                        >
                          {' '}
                          {t('accountType.per_month')}
                        </span>
                      </Title>
                      <Text
                        className={`${styles.planDescription} ${plan.highlight ? styles.planDescriptionHightlighted : ''}`}
                      >
                        {plan.description}
                      </Text>
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          </Col>
        </Row>
      </div>
    </div>
  );
};

export default AccountType;
