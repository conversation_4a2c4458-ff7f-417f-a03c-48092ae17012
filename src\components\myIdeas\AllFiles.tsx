import { Col, Divider, Row, Typography } from 'antd';
import FileCard from '../FileCard/FileCard';
import styles from './AllFiles.module.css';
import { FolderData } from './FolderCard';

const { Text } = Typography;

const AllFiles = ({ folderDetails }: { folderDetails?: FolderData | null }) => {
  return (
    <div className={styles.container}>
      <div className={styles.documentsContainer}>
        <Text className={styles.documents}>Documents</Text>
        <Divider className={styles.documentsDivider} />
      </div>
      <Row gutter={[12, 16]}>
        {folderDetails?.files?.map(file => (
          <Col key={file.uid} xl={6} lg={8} md={12} sm={12} xs={12}>
            <FileCard
              key={file.uid}
              filename={file.file_name}
              size={String(file.file_size)}
              style={{ minWidth: '100%' }}
              file_url={file.file_url}
            />
          </Col>
        ))}
      </Row>
    </div>
  );
};

export default AllFiles;
