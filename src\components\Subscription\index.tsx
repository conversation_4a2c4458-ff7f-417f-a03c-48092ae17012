'use client';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Card, Col, Row, Typography } from 'antd';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';
import { images } from '../../config/images';
import { useTranslation } from '../../hooks/useTranslation';
import styles from './subscription.module.css';
const { Title, Text } = Typography;

const Subscription: React.FC = () => {
  const router = useRouter();
  const { t } = useTranslation();
  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Button
          type='link'
          icon={<ArrowLeftOutlined />}
          className={styles.backButton}
          onClick={() => {
            router.replace('/settings/subscription-plan');
          }}
        >
          {t('common.back')}
        </Button>
      </div>
      <Title level={2} className={styles.heading}>
        {t('subscription.title')}
      </Title>

      <Card className={styles.card}>
        <div className={styles.iconWrapper}>
          <Image src={images.crown} alt='Plan Icon' width={60} height={60} />
        </div>

        <div className={styles.infoGrid}>
          <Row className={styles.infoRow}>
            <Col span={12}>
              <Text className={styles.label}>
                {t('subscription.active_subscription')}
              </Text>
            </Col>
            <Col span={12} className={styles.value}>
              {t('subscription.basic_10mb')}
            </Col>
          </Row>
          <Row className={styles.infoRow}>
            <Col span={12}>
              <Text className={styles.label}>{t('subscription.price')}</Text>
            </Col>
            <Col span={12} className={styles.value}>
              {t('subscription.price_value')}
            </Col>
          </Row>
          <Row className={styles.infoRow}>
            <Col span={12}>
              <Text className={styles.label}>
                {t('subscription.billed_on')}
              </Text>
            </Col>
            <Col span={12} className={styles.value}>
              {t('subscription.july_1_2025')}
            </Col>
          </Row>
          <Row className={styles.infoRow}>
            <Col span={12}>
              <Text className={styles.label}>{t('subscription.status')}</Text>
            </Col>
            <Col span={12}>
              <Text className={styles.active}>{t('subscription.active')}</Text>
            </Col>
          </Row>
          <Row>
            <Col span={12}>
              <Text className={styles.label}>
                {t('subscription.expired_on')}
              </Text>
            </Col>
            <Col span={12} className={styles.value}>
              {t('subscription.expired_description')}
            </Col>
          </Row>
        </div>
      </Card>

      <Button
        type='primary'
        className={styles.bottomButton}
        onClick={() => router.back()}
      >
        {t('common.back')}
      </Button>
    </div>
  );
};

export default Subscription;
