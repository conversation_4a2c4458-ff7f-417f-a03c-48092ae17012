.title {
  font-size: 28px !important;
  font-weight: 600 !important;
  font-family: var(--font-radio-canada) !important;
}

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

:global(html[data-theme='dark']) .container {
  background-color: #012458 !important;
}

.subTitle {
  font-size: 16px !important;
  margin-bottom: 24px !important;
  font-family: var(--font-poppins) !important;
  font-weight: 300 !important;
  color: #555555;
}

:global(html[data-theme='dark']) .subTitle {
  color: #edeff1 !important;
}

@media (max-width: 600px) {
  .title {
    font-size: 22px !important;
  }
  .subTitle {
    font-size: 12px !important;
  }
}
