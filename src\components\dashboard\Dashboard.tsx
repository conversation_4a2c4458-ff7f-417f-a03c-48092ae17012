'use client';

import {
  BulbOutlined,
  FolderOutlined,
  LockOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { <PERSON>ton, Card, Col, Layout, Row, Typography } from 'antd';
import {
  BarElement,
  CategoryScale,
  Chart as ChartJS,
  Title as ChartTitle,
  Legend,
  LinearScale,
  Tooltip,
} from 'chart.js';
import React from 'react';
import { Bar } from 'react-chartjs-2';

import { useTranslation } from '@/hooks/useTranslation';
import '@/styles/dashboard-overrides.css';
import { colors } from '@/theme/antdTheme';
import { useRouter } from 'next/navigation';
import styles from './Dashboard.module.css';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  ChartTitle,
  Tooltip,
  Legend
);

const { Content } = Layout;
const { Title, Text } = Typography;

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const router = useRouter();

  // Chart data for Document Uploads Over Time
  const documentUploadsData = {
    labels: ['7d', '10d', '14d', '18d', '21d', '24d', '28d'],
    datasets: [
      {
        label: t('dashboard.uploaded'),
        data: [25, 35, 30, 45, 50, 40, 55],
        backgroundColor: colors.primary,
        borderRadius: 4,
        barThickness: 20,
      },
      {
        label: t('dashboard.sealed'),
        data: [15, 25, 20, 35, 40, 30, 45],
        backgroundColor: colors.secondary,
        borderRadius: 4,
        barThickness: 20,
      },
    ],
  };

  // Chart data for Folder & Idea Activity
  const folderActivityData = {
    labels: ['7d', '10d', '14d', '18d', '21d', '24d', '28d'],
    datasets: [
      {
        label: t('dashboard.newFolder'),
        data: [20, 30, 25, 40, 45, 35, 50],
        backgroundColor: colors.primary,
        borderRadius: 4,
        barThickness: 20,
      },
      {
        label: t('dashboard.uploads'),
        data: [30, 40, 35, 50, 55, 45, 60],
        backgroundColor: colors.secondary,
        borderRadius: 4,
        barThickness: 20,
      },
      {
        label: t('dashboard.seals'),
        data: [10, 20, 15, 25, 30, 20, 35],
        backgroundColor: colors.black,
        borderRadius: 4,
        barThickness: 20,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
          font: {
            size: 12,
          },
        },
      },
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
      },
      y: {
        beginAtZero: true,
        grid: {
          color: '#f0f0f0',
        },
        ticks: {
          stepSize: 20,
        },
      },
    },
  };

  const cardConfig = [
    {
      key: 'activeFolders',
      icon: <FolderOutlined style={{ fontSize: 24, color: 'white' }} />,
      iconBg: 'var(--color-primary)',
      title: t('dashboard.goToMyIdeas'),
      subtitle: t('dashboard.foldersWithOngoingActivity'),
      textColor: 'var(--color-text-secondary)',
      titleColor: 'var(--color-text-primary)',
      screen: '/my-ideas',
    },
    {
      key: 'recentUploads',
      icon: <UploadOutlined style={{ fontSize: 24, color: 'white' }} />,
      iconBg: 'var(--color-primary)',
      title: t('dashboard.recentUploads'),
      subtitle: t('dashboard.lastSealedIdeasOrFolders'),
      textColor: 'var(--color-text-secondary)',
      titleColor: 'var(--color-text-primary)',
      screen: '/my-ideas?type=recent_ideas',
    },
    {
      key: 'sealedDocuments',
      icon: <LockOutlined style={{ fontSize: 24, color: 'white' }} />,
      iconBg: 'var(--color-primary)',
      bgColor: 'var(--color-white)',
      title: t('dashboard.sealedDocuments'),
      subtitle: t('dashboard.sealedAndTimestampedFiles'),
      textColor: 'var(--color-text-secondary)',
      titleColor: 'var(--color-text-primary)',
      screen: '/my-ideas?type=sealed_documents',
    },
    {
      key: 'ideaTemplates',
      icon: <FolderOutlined style={{ fontSize: 24, color: 'white' }} />,
      iconBg: 'var(--color-primary)',
      bgColor: 'var(--color-white)',
      title: t('dashboard.draftFolders'),
      subtitle: t('dashboard.templatesForYourIdea'),
      textColor: 'var(--color-text-secondary)',
      titleColor: 'var(--color-text-primary)',
      screen: '/my-ideas?type=draft',
    },
  ];

  return (
    <div>
      <Content className={`dashboard-content ${styles.dashboardContent}`}>
        {/* Overview Section */}
        <div className={`overview-section ${styles.overviewSection}`}>
          <Title
            level={2}
            style={{
              fontSize: 'var(--font-xl)',
              fontWeight: 600,
              marginBottom: '24px',
              color: 'var(--color-text-primary)',
            }}
          >
            {t('dashboard.overview')}
          </Title>

          <Row gutter={[16, 16]}>
            {cardConfig.map(card => (
              <Col key={card.key} xs={24} md={12} lg={6} xl={6}>
                <Card
                  className={`overview-card ${styles.overviewCard}`}
                  style={{
                    background: card.bgColor,
                    borderRadius: '12px',
                    border:
                      card.bgColor === colors.secondary
                        ? 'none'
                        : '1px solid var(--color-border)',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                  }}
                  onClick={() => {
                    router.push(card.screen);
                  }}
                  styles={{ body: { padding: '20px' } }}
                  hoverable
                >
                  <div style={{ textAlign: 'left' }}>
                    <div
                      style={{
                        width: 48,
                        height: 48,
                        background: card.iconBg,
                        borderRadius: 8,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        // margin: '0 auto 16px',
                        margin: 'auto 0px 16px 0px',
                      }}
                    >
                      {card.icon}
                    </div>
                    <Title
                      level={4}
                      style={{
                        fontSize: 'var(--font-base)',
                        fontWeight: 600,
                        marginBottom: 8,
                        color: card.titleColor,
                      }}
                    >
                      {card.title}
                    </Title>
                    <Text
                      style={{
                        fontSize: 'var(--font-sm)',
                        color: card.textColor,
                      }}
                    >
                      {card.subtitle}
                    </Text>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>

        {/* My Ideas Section */}
        <div className={styles.myIdeasSection} style={{ marginTop: '32px' }}>
          <Title
            level={2}
            style={{
              fontSize: 'var(--font-xl)',
              fontWeight: 600,
              marginBottom: '24px',
              color: 'var(--color-text-primary)',
            }}
          >
            {t('dashboard.myIdeas')}
          </Title>

          <Row gutter={[16, 16]}>
            {/* Safe New Idea Card */}
            <Col xs={12} md={12} lg={6} xl={6}>
              <Card
                className={`idea-card ${styles.ideaCard}`}
                style={{
                  background: '#E8E8E8',
                  borderRadius: '12px',
                  border: '1px solid var(--color-border)',
                  cursor: 'pointer',
                  transition: 'all 0.3s ease',
                  minHeight: '200px',
                }}
                styles={{ body: { padding: '20px', height: '100%' } }}
                hoverable
              >
                <div
                  style={{
                    textAlign: 'center',
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'center',
                  }}
                >
                  <BulbOutlined
                    style={{
                      fontSize: '32px',
                      color: '#FFD700',
                      marginBottom: '16px',
                    }}
                  />
                  <Title
                    level={4}
                    style={{
                      fontSize: 'var(--font-base)',
                      fontWeight: 600,
                      marginBottom: '8px',
                      color: 'var(--color-text-primary)',
                    }}
                  >
                    {t('dashboard.safeNewIdea')}
                  </Title>
                  <Text
                    style={{
                      fontSize: 'var(--font-sm)',
                      color: 'var(--color-text-secondary)',
                    }}
                  >
                    {t('dashboard.safeYourNewIdeaOrCreateFolder')}
                  </Text>
                </div>
              </Card>
            </Col>

            {/* Idea Cards */}
            {[1, 2, 3].map(index => (
              <Col xs={12} md={12} lg={6} xl={6} key={index}>
                <Card
                  className={`idea-card ${styles.ideaCard}`}
                  style={{
                    background: 'var(--color-white)',
                    borderRadius: '12px',
                    border: '1px solid var(--color-border)',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    minHeight: '200px',
                  }}
                  styles={{ body: { padding: '20px', height: '100%' } }}
                  hoverable
                >
                  <div
                    style={{
                      height: '100%',
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'space-between',
                    }}
                  >
                    <div>
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignItems: 'flex-start',
                          marginBottom: '16px',
                        }}
                      >
                        <BulbOutlined
                          style={{
                            fontSize: '24px',
                            color: '#FFD700',
                          }}
                        />
                        <Button
                          type='text'
                          size='small'
                          style={{ color: '#ff4d4f', padding: 0 }}
                        >
                          ×
                        </Button>
                      </div>
                      <Title
                        level={4}
                        style={{
                          fontSize: 'var(--font-base)',
                          fontWeight: 600,
                          marginBottom: '8px',
                          color: 'var(--color-text-primary)',
                        }}
                      >
                        {t('dashboard.ideaName')}
                      </Title>
                      <Text
                        style={{
                          fontSize: 'var(--font-sm)',
                          color: 'var(--color-text-secondary)',
                          display: 'block',
                          marginBottom: '16px',
                        }}
                      >
                        {t('dashboard.sedUtPerspiciatisUndeNisIsteNatus')}
                      </Text>
                    </div>
                    <div>
                      <Text
                        style={{
                          fontSize: 'var(--font-xs)',
                          color: index === 2 ? '#52c41a' : '#1890ff',
                          fontWeight: 500,
                        }}
                      >
                        {index === 2
                          ? t('dashboard.sealedOnBlockchain')
                          : t('dashboard.inDrafts')}
                      </Text>
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </div>

        {/* Your Document Activity Section */}
        <div
          className={styles.documentActivitySection}
          style={{ marginTop: '32px' }}
        >
          <Title
            level={2}
            style={{
              fontSize: 'var(--font-xl)',
              fontWeight: 600,
              marginBottom: '24px',
              color: 'var(--color-text-primary)',
            }}
          >
            {t('dashboard.yourDocumentActivity')}
          </Title>

          <Row gutter={[24, 24]}>
            {/* Document Uploads Over Time */}
            <Col xs={24} lg={12}>
              <Card title={t('dashboard.documentUploadsOverTime')}>
                <div style={{ height: '300px' }}>
                  <Bar data={documentUploadsData} options={chartOptions} />
                </div>
              </Card>
            </Col>

            {/* Folder & Idea Activity */}
            <Col xs={24} lg={12}>
              <Card title={t('dashboard.folderAndIdeaActivity')}>
                <div style={{ height: '300px' }}>
                  <Bar data={folderActivityData} options={chartOptions} />
                </div>
              </Card>
            </Col>
          </Row>
        </div>
      </Content>
    </div>
  );
};

export default Dashboard;
