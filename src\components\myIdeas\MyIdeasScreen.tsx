'use client';

import { SearchOutlined } from '@ant-design/icons';
import { Button, Col, Input, Row, Spin, Typography } from 'antd';
import { useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { Endpoints } from '@/config/endpoints';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import { useTranslation } from '@/hooks/useTranslation';
import useMediaQuery from '../../hooks/useMediaQuery';
import { useNotification } from '../Notification/NotificationContext';
import CreateNewFolderCard from './CreateNewFolderCard';
import FolderCard, { FolderData } from './FolderCard';
import styles from './MyIdeasScreen.module.css';

const { Title } = Typography;

interface FolderResponse extends ApiResponse {
  data: FolderData[];
  pagination: {
    currentPage?: number;
    isMore?: boolean;
    pageSize?: number;
    totalCount?: number | null;
    totalPage?: number | null;
  };
}

/**
 * Filter type for ideas filtering
 */
type FilterType =
  | 'allIdeas'
  | 'recent_ideas'
  | 'sealed_documents'
  | 'draft'
  | 'in_review';

interface PaginationProps {
  currentPage: number;
  isMore: boolean;
  pageSize: number;
  totalCount: number | null;
  totalPage: number | null;
}

interface FolderFormData {
  _id?: string;
  saved_folder?: boolean;
}

/**
 * MyIdeasScreen component - Main screen for displaying and managing ideas
 *
 * @component MyIdeasScreen
 * @returns {JSX.Element} Rendered My Ideas screen component
 *
 * @example
 * <MyIdeasScreen />
 *
 * @description
 * This component provides the main interface for users to:
 * - View all their idea folders in a grid layout
 * - Search through ideas using the search bar
 * - Filter ideas by status (All Ideas, Recent Ideas, Sealed Documents, Draft, In Review)
 * - Create new folders via the create new folder card
 * - Edit and delete existing folders via context menu
 * - Navigate to individual folder views
 */
const MyIdeasScreen: React.FC = () => {
  const { t } = useTranslation();
  const notification = useNotification();
  const searchParams = useSearchParams();
  const type = searchParams.get('type') || '';
  console.log('params ===>', type);

  const router = useRouter();
  const [loader, setLoader] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilter, setActiveFilter] = useState<FilterType | string>(
    type || 'allIdeas'
  );
  const [folders, setFolders] = useState<FolderData[]>([]);
  const [pagination, setPagination] = useState<PaginationProps>({
    currentPage: 1,
    isMore: false,
    pageSize: 10,
    totalCount: null,
    totalPage: null,
  });

  const filterOptions: { key: FilterType; label: string }[] = [
    { key: 'allIdeas', label: t('myIdeas.filters.allIdeas') },
    { key: 'recent_ideas', label: t('myIdeas.filters.recentIdeas') },
    { key: 'sealed_documents', label: t('myIdeas.filters.sealedDocuments') },
    { key: 'draft', label: t('myIdeas.filters.draft') },
    { key: 'in_review', label: t('myIdeas.filters.inReview') },
  ];

  const handleSearch = (value: string) => {
    setSearchQuery(value);
    getFolders(activeFilter, value);
    // TODO: Implement search functionality
  };

  const handleFilterChange = (filter: FilterType) => {
    setActiveFilter(filter);
    getFolders(filter);
    // TODO: Implement filtering functionality
  };

  const handleFolderClick = (folder: FolderData) => {
    // TODO: Navigate to folder detail view
    console.log('Open folder:', folder);
    // setState(p => ({ ...p, upload: true }));
    router.push(`/my-ideas/view/${folder?._id}`);
  };

  const getFolders = async (
    type: FilterType | string = 'allIdeas',
    searchVal?: string,
    page?: number,
    append: boolean = false
  ) => {
    setLoader(true);
    try {
      let apiData = {};
      if (type === 'allIdeas') {
        apiData = {
          page: 1,
          searchVal,
        };
      } else {
        apiData = {
          page: 1,
          type,
          searchVal,
        };
      }
      const response = await getApiData<
        {
          page?: number | string;
          searchVal?: string;
          type?: string;
        },
        FolderResponse
      >({
        url: Endpoints.getFolderList,
        method: 'POST',
        data: apiData,
      });

      if (response && response.status === true && response?.data) {
        setFolders(prev =>
          append ? [...prev, ...response.data] : response.data
        );
        const pagination = response.pagination;
        // setPagination(response.pagination);

        if (response.pagination) {
          setPagination(prev => ({
            ...prev,
            currentPage: pagination?.currentPage ?? prev.currentPage ?? 1, // fallback to prev or 1
            isMore: pagination?.isMore ?? prev.isMore,
            totalPage: pagination?.totalPage ?? prev.totalPage,
            pageSize: pagination?.pageSize ?? prev.pageSize,
            totalCount: pagination?.totalCount ?? prev.totalCount,
          }));
        }
      } else {
        notification.error({
          message: 'Error',
          description: response?.message || t('myIdeas.error'),
        });
      }

      setLoader(false);
    } catch (error) {
      setLoader(false);
      notification.error({
        message: 'Error',
        description: error?.message || t('error'),
      });
      console.error('Error fetching folders:', error);
    }
  };

  useEffect(() => {
    getFolders(activeFilter);
    return () => {};
  }, []);

  const loadMore = () => {
    const nextPage = pagination.currentPage + 1;
    getFolders(activeFilter, searchQuery, nextPage, true);
  };

  const handleEditFolder = (folder: FolderData) => {
    // Navigate to edit folder page with prefilled data
    router.push(`/my-ideas/edit/${folder._id}`);
  };

  const handleDeleteFolder = (folder: FolderData) => {
    // Navigate to edit folder page with prefilled data
    router.push(`/my-ideas/delete/${folder._id}`);

    // Modal.confirm({
    //   title: t('myIdeas.deleteConfirmation.title'),
    //   content: t('myIdeas.deleteConfirmation.message'),
    //   okText: t('myIdeas.deleteConfirmation.confirm'),
    //   cancelText: t('myIdeas.deleteConfirmation.cancel'),
    //   okType: 'danger',
    //   onOk: () => {
    //     setFolders(prev => prev.filter(f => f._id !== folder._id));
    //   },
    // });
  };

  const handleSaveFolder = async (folder: FolderData) => {
    try {
      const res = await getApiData<FolderFormData, ApiResponse>({
        url: Endpoints.saveFolder,
        method: 'POST',
        data: { _id: folder._id, saved_folder: true },
      });

      if (res && res.status === true) {
        notification.success({
          message: res?.message || 'Folder saved successfully',
        });
      } else {
        notification.error({
          message: res?.message || 'Error saving folder',
        });
      }
    } catch (error) {
      notification.error({
        message: error.message || 'Error saving folder',
      });
      console.error('Error saving folder:', error);
    }
  };

  const sm = useMediaQuery('(max-width: 600px)');

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Row>
          <Col xs={24} sm={24} md={7} lg={7} xl={4}>
            <Title level={1} className={styles.pageTitle}>
              {t('myIdeas.title')}
            </Title>
          </Col>
          <Col xs={24} sm={24} md={17} lg={17} xl={20}>
            <Input
              placeholder={t('myIdeas.searchPlaceholder')}
              prefix={<SearchOutlined />}
              value={searchQuery}
              onChange={e => {
                setSearchQuery(e.target.value);
                handleSearch(e.target.value);
              }}
              onClear={() => handleSearch('')}
              allowClear
              className={styles.searchInput}
            />
          </Col>
        </Row>

        <div className={styles.searchAndFilters}>
          <div className={styles.filterButtons}>
            {filterOptions.map(option => (
              <Button
                key={option.key}
                className={`${styles.filterButton} ${activeFilter === option.key ? styles.filterButtonActive : ''}`}
                onClick={() => handleFilterChange(option.key)}
              >
                {option.label}
              </Button>
            ))}
          </div>
        </div>
      </div>
      {loader ? (
        <div className={styles.foldersSpinner}>
          <Spin size='default' />
        </div>
      ) : (
        <div className={styles.foldersGrid}>
          <Row gutter={sm ? [12, 12] : [24, 24]}>
            {folders.map(folder => (
              <Col key={folder._id} xs={12} sm={12} md={12} lg={12} xl={6}>
                <FolderCard
                  folder={folder}
                  onClick={handleFolderClick}
                  onEdit={handleEditFolder}
                  onDelete={handleDeleteFolder}
                  onSave={handleSaveFolder}
                  style={{
                    minWidth: '100%',
                  }}
                />
              </Col>
            ))}

            <Col xs={12} sm={12} md={12} lg={12} xl={6}>
              <CreateNewFolderCard />
            </Col>
          </Row>

          {pagination.isMore && (
            <div className={styles.loadMoreWrapper}>
              <Button onClick={loadMore} loading={loader}>
                {t('myIdeas.loadMore') || 'Load More'}
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default MyIdeasScreen;
