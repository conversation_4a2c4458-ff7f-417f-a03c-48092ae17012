import { images } from '@/config/images';
import { useTheme } from '@/contexts/ThemeContext';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { updateUser } from '@/store/slices/authSlice';
import { Button } from 'antd';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';
import { generateToken } from '../../notifications/firebase';
import { useNotification } from '../Notification/NotificationContext';
import styles from './notificationPermission.module.css';

const NotificationPermission: React.FC = () => {
  const { theme } = useTheme();
  const router = useRouter();
  const { t } = useTranslation();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector(state => state.auth);
  const notification = useNotification();
  return (
    <div className={styles.container}>
      {/* Title */}
      <h1 className={styles.title}>{t('notification.turn_on_notification')}</h1>

      {/* Subtitle */}
      <p className={styles.subtitle}>{t('notification.stay_up_to_date')}</p>

      {/* Illustration */}
      <div className={styles.illustration}>
        <Image
          src={
            theme === 'dark'
              ? images.notificationPermissionDark
              : images.notificationPermissionLight
          }
          alt=''
          width={350}
          height={350}
          className={styles.illustrationImg}
        />
      </div>

      {/* Note */}

      {/* Buttons */}
      <div className={styles.buttonGroup}>
        <Button
          type='primary'
          className={styles.agreeButton}
          onClick={async () => {
            const permission = await generateToken();
            if (permission === 'denied') {
              notification.error({
                message: 'Notifications Blocked',
                description:
                  'You have blocked notifications. Please enable them in your browser settings to receive alerts.',
              });
            }
            // Mark onboarding as completed
            dispatch(
              updateUser({
                user: { ...user, last_screen: 'completed' },
                isAuthenticated: true,
              })
            );

            // Clear navigation history and redirect to dashboard
            router.replace('/dashboard');
            setTimeout(() => {
              if (typeof window !== 'undefined') {
                window.history.replaceState(null, '', '/dashboard');
              }
            }, 100);
          }}
        >
          {t('notification.enable_notification')}
        </Button>
        <Button
          variant='outlined'
          className={styles.primaryButton}
          onClick={() => {
            // Mark onboarding as completed even if notification is skipped
            dispatch(
              updateUser({
                user: { ...user, last_screen: 'completed' },
                isAuthenticated: true,
              })
            );

            // Clear navigation history and redirect to dashboard
            router.replace('/dashboard');
            setTimeout(() => {
              if (typeof window !== 'undefined') {
                window.history.replaceState(null, '', '/dashboard');
              }
            }, 100);
          }}
        >
          {t('notification.maybe_later')}
        </Button>
      </div>
    </div>
  );
};

export default NotificationPermission;
