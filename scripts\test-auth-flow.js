/**
 * Authentication Flow Test Script
 * Run this in browser console to test various authentication states
 */

class AuthFlowTester {
  constructor() {
    this.baseUrl = window.location.origin;
    this.testResults = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
    console.log(logMessage);
    this.testResults.push({ timestamp, type, message });
  }

  // Clear all auth-related cookies and localStorage
  clearAuthState() {
    document.cookie = 'auth-token=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
    document.cookie = 'user-data=; path=/; expires=Thu, 01 Jan 1970 00:00:01 GMT';
    localStorage.removeItem('auth-state');
    localStorage.removeItem('token');
    this.log('Cleared all authentication state');
  }

  // Set user state for testing
  setUserState(state) {
    const { isAuthenticated, userData } = state;
    
    if (isAuthenticated) {
      document.cookie = 'auth-token=authenticated; path=/; max-age=604800'; // 7 days
    }
    
    if (userData) {
      document.cookie = `user-data=${encodeURIComponent(JSON.stringify(userData))}; path=/; max-age=604800`;
    }
    
    this.log(`Set user state: ${JSON.stringify(state)}`);
  }

  // Test scenarios
  async testUnauthenticatedAccess() {
    this.log('Testing unauthenticated access...', 'test');
    this.clearAuthState();
    
    const protectedRoutes = ['/dashboard', '/steps', '/notification-permission'];
    
    for (const route of protectedRoutes) {
      try {
        window.history.pushState({}, '', route);
        await new Promise(resolve => setTimeout(resolve, 100));
        
        if (window.location.pathname === '/login') {
          this.log(`✅ ${route} correctly redirected to login`, 'success');
        } else {
          this.log(`❌ ${route} did not redirect to login`, 'error');
        }
      } catch (error) {
        this.log(`❌ Error testing ${route}: ${error.message}`, 'error');
      }
    }
  }

  async testIncompleteOnboarding() {
    this.log('Testing incomplete onboarding flow...', 'test');
    
    // Test user at step 2
    this.setUserState({
      isAuthenticated: true,
      userData: {
        email: '<EMAIL>',
        isEmailVerified: true,
        is2FAEnabled: '1',
        last_screen: 'screen_2_goals'
      }
    });

    // Try accessing dashboard - should redirect to step 3
    window.history.pushState({}, '', '/dashboard');
    await new Promise(resolve => setTimeout(resolve, 100));
    
    if (window.location.pathname === '/steps' && window.location.search.includes('step=2')) {
      this.log('✅ Dashboard correctly redirected to next onboarding step', 'success');
    } else {
      this.log('❌ Dashboard did not redirect correctly', 'error');
    }
  }

  async testCompletedOnboarding() {
    this.log('Testing completed onboarding flow...', 'test');
    
    this.setUserState({
      isAuthenticated: true,
      userData: {
        email: '<EMAIL>',
        isEmailVerified: true,
        is2FAEnabled: '1',
        last_screen: 'completed'
      }
    });

    // Try accessing onboarding routes - should redirect to dashboard
    const onboardingRoutes = ['/signup', '/steps', '/verify-otp'];
    
    for (const route of onboardingRoutes) {
      window.history.pushState({}, '', route);
      await new Promise(resolve => setTimeout(resolve, 100));
      
      if (window.location.pathname === '/dashboard') {
        this.log(`✅ ${route} correctly redirected to dashboard`, 'success');
      } else {
        this.log(`❌ ${route} did not redirect to dashboard`, 'error');
      }
    }
  }

  testBackButtonPrevention() {
    this.log('Testing back button prevention...', 'test');
    
    // Set completed onboarding state
    this.setUserState({
      isAuthenticated: true,
      userData: {
        email: '<EMAIL>',
        isEmailVerified: true,
        is2FAEnabled: '1',
        last_screen: 'completed'
      }
    });

    // Navigate to dashboard
    window.history.pushState({}, '', '/dashboard');
    
    // Add a dummy history entry to test back navigation
    window.history.pushState({ test: true }, '', '/dashboard');
    
    // Try to go back
    window.history.back();
    
    setTimeout(() => {
      if (window.location.pathname === '/dashboard') {
        this.log('✅ Back navigation correctly prevented', 'success');
      } else {
        this.log('❌ Back navigation was not prevented', 'error');
      }
    }, 200);
  }

  testCookieManagement() {
    this.log('Testing cookie management...', 'test');
    
    // Test setting user data
    const testUserData = {
      email: '<EMAIL>',
      isEmailVerified: true,
      is2FAEnabled: '1',
      last_screen: 'screen_1_tell_about_us'
    };
    
    this.setUserState({
      isAuthenticated: true,
      userData: testUserData
    });
    
    // Check if cookies are set correctly
    const authToken = document.cookie.split('; ').find(row => row.startsWith('auth-token='));
    const userData = document.cookie.split('; ').find(row => row.startsWith('user-data='));
    
    if (authToken && userData) {
      try {
        const parsedUserData = JSON.parse(decodeURIComponent(userData.split('=')[1]));
        if (JSON.stringify(parsedUserData) === JSON.stringify(testUserData)) {
          this.log('✅ Cookies set and parsed correctly', 'success');
        } else {
          this.log('❌ Cookie data does not match expected', 'error');
        }
      } catch (error) {
        this.log(`❌ Error parsing cookie data: ${error.message}`, 'error');
      }
    } else {
      this.log('❌ Required cookies not found', 'error');
    }
  }

  // Run all tests
  async runAllTests() {
    this.log('Starting authentication flow tests...', 'test');
    this.testResults = [];
    
    await this.testUnauthenticatedAccess();
    await this.testIncompleteOnboarding();
    await this.testCompletedOnboarding();
    this.testBackButtonPrevention();
    this.testCookieManagement();
    
    // Summary
    const successCount = this.testResults.filter(r => r.type === 'success').length;
    const errorCount = this.testResults.filter(r => r.type === 'error').length;
    
    this.log(`Test Summary: ${successCount} passed, ${errorCount} failed`, 'test');
    
    if (errorCount === 0) {
      this.log('🎉 All tests passed!', 'success');
    } else {
      this.log('⚠️ Some tests failed. Check the logs above.', 'error');
    }
    
    return this.testResults;
  }

  // Get test results
  getResults() {
    return this.testResults;
  }
}

// Usage instructions
console.log(`
🧪 Authentication Flow Tester Loaded!

Usage:
const tester = new AuthFlowTester();

// Run all tests
await tester.runAllTests();

// Run individual tests
await tester.testUnauthenticatedAccess();
await tester.testIncompleteOnboarding();
await tester.testCompletedOnboarding();
tester.testBackButtonPrevention();
tester.testCookieManagement();

// Clear auth state
tester.clearAuthState();

// Set custom user state
tester.setUserState({
  isAuthenticated: true,
  userData: {
    email: '<EMAIL>',
    isEmailVerified: true,
    is2FAEnabled: '1',
    last_screen: 'screen_2_goals'
  }
});
`);

// Make tester available globally
window.AuthFlowTester = AuthFlowTester;
