'use client';
import { Endpoints } from '@/config/endpoints';
import { images } from '@/config/images';
import { ApiResponse, getApiData } from '@/helpers/ApiHelper';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import { ArrowLeftOutlined, CheckOutlined } from '@ant-design/icons';
import { Button, Col, Form, Row, Typography } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import Image from 'next/image';
import { useParams, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { isMobile } from 'react-device-detect';
import { useTheme } from '../../contexts/ThemeContext';
import { useNotification } from '../Notification/NotificationContext';
import styles from './ReadyToSeal.module.css';

const { Title, Text } = Typography;

interface FileData {
  _id: string;
  file_url: string;
  file_name: string;
  file_type: string;
  file_size: string;
  status: string;
}

interface FolderFormData {
  name: string;
  mobile_number: string;
  color: string;
  description: string;
  country_code?: string;
  id?: string;
  files?: FileData[];
  status?: string;
  notes?: string;
}

interface PlanData {
  _id: string;
  name: string;
  amount: number;
  title: string;
  product_id: string;
  price_id: string;
  is_active: boolean;
  from: string;
  created_by: string;
  createdAt: number;
  updatedAt: number;
  flag: string;
}

const blankSelectedData = {
  _id: '',
  name: '',
  amount: 0,
  title: '',
  product_id: '',
  price_id: '',
  is_active: false,
  from: '',
  created_by: '',
  createdAt: 0,
  updatedAt: 0,
  flag: '',
};

const ReadyToSealScreen = () => {
  const params = useParams();
  const ideaId = params.id as string;
  const notification = useNotification();
  const [form] = Form.useForm();
  const { t } = useTranslation();

  const [selectedDocs, setSelectedDocs] = useState<string[]>([]);
  const [sealLoader, setSealLoader] = useState(false);
  const [selectedCountry, setSelectedCountry] = useState<PlanData | null>(
    blankSelectedData
  );
  const [folderData, setFolderData] = useState<FolderFormData | null>(null);
  const [sealPlans, setSealPlans] = useState<PlanData[] | []>([]);
  const [visibleCount, setVisibleCount] = useState<number>(4); // 👈 show more logic

  const router = useRouter();
  const theme = useTheme();

  const toggleDocs = (docId: string) => {
    const updated = selectedDocs.includes(docId)
      ? selectedDocs.filter((g: string) => g !== docId)
      : [...selectedDocs, docId];
    setSelectedDocs(updated);
  };

  useEffect(() => {
    form.setFieldsValue({
      notes: folderData?.notes || '',
    });
    return () => {};
  }, [folderData]);

  useEffect(() => {
    if (ideaId) {
      getFolderData(ideaId);
      getSealPlans(ideaId);
    }
  }, [ideaId]);

  const Countries = [
    { id: 'usa', name: 'USA', flag: '/images/USA.png' },
    { id: 'uae', name: 'UAE', flag: '/images/UAE.png' },
    { id: 'swiss', name: 'Switzerland', flag: '/images/Swiss.png' },
    { id: 'europe', name: 'Europe', flag: '/images/Europe.png' },
  ];

  const sm = useMediaQuery('(max-width: 768px)');
  const xs = useMediaQuery('(max-width: 600px)');

  const getSealPlans = async (id: string) => {
    try {
      const res = await getApiData<FolderFormData, ApiResponse>({
        url: `${Endpoints.getSealPlans}?type=user_seal_payment`,
        method: 'GET',
      });
      console.log('res =====>', res);

      if (res && res.status === true && res.data) {
        setSealPlans(res.data);
      } else {
        console.log(res?.message);
        notification.error({
          message: res?.message || 'Error fetching seal plans',
        });
      }
    } catch (error) {
      notification.error({
        message: error?.message || 'Error fetching seal plans',
      });
      console.error('Error fetching folder data:', error);
    }
  };

  const getFolderData = async (id: string) => {
    try {
      const res = await getApiData<FolderFormData, ApiResponse>({
        url: `${Endpoints.getFolderById}/${id}`,
        method: 'GET',
      });
      if (res && res.status === true && res.data) {
        setFolderData(res.data);
      } else {
        console.log(res?.message);
      }
    } catch (error) {
      console.error('Error fetching folder data:', error);
    }
  };

  interface SealParams {
    folder_id: string;
    country?: string;
    notes?: string;
    plan_id?: string;
  }

  const handleSeal = async () => {
    if (!selectedCountry?._id) {
      notification.error({
        message: t('paymentStatus.selectMethod'),
      });
      return;
    }
    const data = await form.getFieldsValue();
    setSealLoader(true);
    try {
      const res = await getApiData<SealParams, ApiResponse>({
        url: Endpoints.createCheckoutSession,
        method: 'POST',
        data: {
          folder_id: ideaId,
          // country: selectedCountry,
          notes: data.notes,
          plan_id: selectedCountry?._id,
        },
      });
      if (res && res.status === true) {
        console.log('res', res);
        notification.success({
          message: res?.message || 'Folder sealed successfully',
        });
        if (res.data?.payment_url) {
          // router.push(res.data.payment_url); // ✅ Works for internal routes
          window.location.href = res.data.payment_url;
        }
        // router.push(`/my-ideas/view/${ideaId}?type=success`);
        // getFolderData(ideaId);
      } else {
        console.log(res?.message);
        notification.error({
          message: 'Error',
          description: res?.message || t('common.error'),
        });
      }
      setSealLoader(false);
    } catch (error) {
      notification.error({
        message: 'Error',
        description: error?.message || t('common.error'),
      });
      setSealLoader(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <Row>
          <Col
            sm={24}
            style={{
              display: sm ? 'flex' : 'block',
              alignItems: sm ? 'center' : 'flex-start',
              justifyContent: sm ? 'center' : 'flex-start',
              width: sm ? '100%' : '50%',
            }}
          >
            {sm && (
              <ArrowLeftOutlined
                style={{
                  fontSize: '20px',
                  color: '#000',
                  position: 'absolute',
                  left: xs ? '0px' : '20px',
                }}
                onClick={() => {}}
              />
            )}

            <div style={{ textAlign: sm ? 'center' : 'left' }}>
              <Title level={1} className={styles.pageTitle}>
                {folderData?.name}
              </Title>
              <Text className={styles.subTitle}>
                {t('readyToSeal.subTitle')}
              </Text>
            </div>
          </Col>

          {/* Documents */}
          <div className={styles.docList}>
            {folderData?.files?.slice(0, visibleCount).map(doc => {
              const isSelected = selectedDocs.includes(doc._id);
              return (
                <div
                  key={doc._id}
                  className={`${styles.docItem} ${isSelected ? styles.selected : ''}`}
                  onClick={() => toggleDocs(doc._id)}
                >
                  <Row className={styles.docCardRow}>
                    <div className={styles.iconWrap}>
                      <Image
                        src={images.file}
                        alt={doc.file_name}
                        width={isMobile ? 45 : 55}
                        height={isMobile ? 45 : 55}
                      />
                    </div>
                    <div
                      style={{
                        display: 'flex',
                        flexDirection: 'column',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      <span className={styles.label}>{doc.file_name}</span>
                    </div>
                  </Row>
                  {isSelected && (
                    <div className={styles.checkIcon}>
                      <CheckOutlined />
                    </div>
                  )}
                </div>
              );
            })}

            {/* Show More Button */}
            {folderData?.files && (
              <div style={{ textAlign: 'center', marginTop: '5px' }}>
                {visibleCount < folderData.files.length ? (
                  <Button
                    type='link'
                    onClick={() => setVisibleCount(prev => prev + 4)}
                    className={styles.viewMoreButton}
                    style={{ fontWeight: 'bold', color: '#003366' }}
                  >
                    {t('readyToSeal.view_more')} (
                    {folderData.files.length - visibleCount}{' '}
                    {t('readyToSeal.more_files')})
                  </Button>
                ) : folderData.files.length > 4 ? (
                  <Button
                    type='link'
                    onClick={() => setVisibleCount(4)}
                    className={styles.viewMoreButton}
                    style={{ fontWeight: 'bold', color: '#003366' }}
                  >
                    {t('readyToSeal.view_less')}
                  </Button>
                ) : null}
              </div>
            )}
          </div>

          <Form
            form={form}
            layout='vertical'
            autoComplete='off'
            initialValues={{ notes: folderData?.notes || '' }}
            style={{ width: '100%' }}
          >
            <Form.Item
              name='notes'
              label={
                <Text className={styles.formLabel}>{t('draftIdea.notes')}</Text>
              }
            >
              <TextArea rows={6} className={styles.textArea} />
            </Form.Item>
          </Form>

          {/* Country selection */}
          <div
            style={{
              display: 'flex',
              alignItems: 'flex-start',
              justifyContent: 'space-between',
              width: '100%',
            }}
          >
            <div>
              <label className={styles.formLabel}>
                {t('readyToSeal.seal_method')}
              </label>
              <div className={styles.countryList}>
                {sealPlans?.map(country => {
                  const isSelected = selectedCountry?._id === country?._id;
                  return (
                    <div
                      key={country?._id}
                      className={`${styles.countryItem} ${isSelected ? styles.selected : ''}`}
                      onClick={() => setSelectedCountry(country)}
                      style={{
                        backgroundColor:
                          theme?.theme === 'dark' ? 'transparent' : '#fff',
                      }}
                    >
                      <div className={styles.flagContainer}>
                        <Image
                          src={country?.flag || '/images/swiss.png'}
                          alt={country.name}
                          width={74}
                          height={74}
                          style={{
                            objectFit: 'cover',
                            width: '100%',
                            height: '100%',
                          }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
            <div className={styles.priceContainer}>
              <div>
                <Text className={styles.priceText}>
                  {t('readyToSeal.one_time_cost')}
                </Text>
              </div>
              <Text className={styles.price}>$20</Text>
            </div>
          </div>

          {/* Price and actions */}

          <div className={styles.btnParent}>
            <div
              className={styles.buttonGroup}
              style={{ width: sm ? '100%' : '450px' }}
            >
              <Button
                type='primary'
                onClick={() => {
                  handleSeal();
                }}
                loading={sealLoader}
                style={{
                  width: '100%',
                }}
              >
                {t('readyToSeal.seal_now')}
              </Button>
              <Button
                type='default'
                onClick={() => {}}
                style={{
                  width: '100%',
                }}
              >
                {t('readyToSeal.cancel')}
              </Button>
            </div>
          </div>
        </Row>
      </div>
    </div>
  );
};

export default ReadyToSealScreen;
