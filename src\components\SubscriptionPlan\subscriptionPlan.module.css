.main {
  align-items: center;
  justify-content: center;
  display: flex;
  padding: 10px;
}
.container {
  /* padding: 40px; */
  align-items: center;
  justify-content: center;
  min-height: 90vh;
  background-color: var(--color-background);
}
.inner {
  align-items: center;
  justify-content: center;
  display: flex;
  align-self: center;
}

.heading {
  text-align: center;
  margin-bottom: 40px;
  font-size: 28px;
  padding: 40px 0px 0px;
}

.backButton {
  margin: 0;
  font-size: 16px;
  color: var(--color-text-primary) !important;
  height: auto;
  padding: 20px 0px 0px;
}

.backButton:hover {
  color: var(--color-text-primary);
}
.card {
  border-radius: 12px;
  transition: all 0.3s ease;
  background: var(--color-card) !important;
  cursor: pointer;
  transition: all 0.3s ease;
  /* width: 50%; */
}

:global(html[data-theme='dark']) .card {
  background-color: rgb(77, 102, 138) !important;
}
:global(html[data-theme='light']) .card {
  background-color: #edeff1 !important;
}

.card:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 24px rgba(116, 150, 202, 0.3) !important;
  /* box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15); */
}

.price {
  font-size: 24px;
  margin: 5px 0;
  display: flex;
  align-items: center;
  font-weight: bold;
}
.month {
  font-size: 16px;
  padding-left: 5px;
  color: var(--color-text-tertiary);
  font-weight: 200;
}
.title {
  font-size: 24px;
}
.description {
  font-size: 16px;
  /* margin-bottom: 16px; */
}
