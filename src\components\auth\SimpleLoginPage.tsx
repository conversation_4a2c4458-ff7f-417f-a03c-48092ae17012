'use client';

import {
  AppleFilled,
  GoogleCircleFilled,
  LinkedinFilled,
} from '@ant-design/icons';
import { Button, Checkbox, Divider, Form, Input, Typography } from 'antd';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import AuthWrapper, { authStyles } from '@/components/auth/AuthWrapper';
import { Endpoints } from '@/config/endpoints';
import { images } from '@/config/images';
import siteConfig from '@/config/site.config';
import { useTheme } from '@/contexts/ThemeContext';
import { getApiData } from '@/helpers/ApiHelper';
import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { loginFailure } from '@/store/slices/authSlice';
import { validateLoginForm } from '@/utils/validation';
import {
  CredentialResponse,
  TokenResponse,
  useGoogleLogin,
} from '@react-oauth/google';
import { isEmpty } from 'lodash-es';
import Image from 'next/image';
import AppleSignin from 'react-apple-signin-auth';
import { isMobile, isTablet } from 'react-device-detect';
import { LinkedIn } from 'react-linkedin-login-oauth2';
import FacebookLogin from '../FacebookLogin';
import { useNotification } from '../Notification/NotificationContext';
import { handleRoute, handleSocialLogin, LoginResponse } from './AuthFunction';
const { Title, Text } = Typography;

interface LoginPageProps {
  onLoginSuccess?: () => void;
  type?: string;
}

const SimpleLoginPage: React.FC<LoginPageProps> = () => {
  const [form] = Form.useForm();
  const notification = useNotification();
  const [rememberMe, setRememberMe] = useState(false);
  const [loader, setLoading] = useState(false);
  const [socialLoader, setSocialLoading] = useState('');
  const { theme } = useTheme();

  const dispatch = useAppDispatch();
  const { loading, error } = useAppSelector(state => state.auth);
  const router = useRouter();

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      if (event.data?.type === 'LINKEDIN_AUTH_SUCCESS') {
        const { code } = event.data;
        const data = {
          credential: code,
        };
        handleSuccess(data, 'linkedIn');
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, []);

  // Load saved credentials on component mount
  useEffect(() => {
    const savedCredentials = localStorage.getItem('rememberedCredentials');
    if (savedCredentials && user?.role === 'individual') {
      try {
        const {
          email,
          password,
          rememberMe: savedRememberMe,
        } = JSON.parse(savedCredentials);
        if (savedRememberMe) {
          form.setFieldsValue({ email, password, rememberMe: savedRememberMe });
          setRememberMe(true);
        }
      } catch (error) {
        console.error('Error loading saved credentials', error);
        localStorage.removeItem('rememberedCredentials');
      }
    }
  }, [form]);

  const { user } = useAppSelector(state => state.auth);

  const { t } = useTranslation();

  interface AppleResponse {
    authorization: {
      id_token: string;
    };
    user?: {
      name?: {
        firstName?: string;
        lastName?: string;
      };
    };
  }
  interface SocialLoginPayload {
    token: string;
    type: string;
    role: string;
    full_name_obj?: object;
  }
  const handleSubmit = async (values: {
    email: string;
    password: string;
    role?: string;
  }) => {
    const validation = validateLoginForm(values?.email, values?.password);
    if (!validation.isValid) {
      dispatch(loginFailure(validation.errors.join(', ')));
      return;
    }

    // Handle Remember Me functionality
    if (rememberMe) {
      const credentialsToSave = {
        email: values.email,
        password: values.password,
        rememberMe: true,
      };
      localStorage.setItem(
        'rememberedCredentials',
        JSON.stringify(credentialsToSave)
      );
    } else {
      localStorage.removeItem('rememberedCredentials');
    }
    setLoading(true);

    try {
      const response = await getApiData<typeof values, LoginResponse>({
        url: `${siteConfig.apiUrl}${Endpoints.login}`,
        method: 'POST',
        data: {
          email: values.email,
          password: values.password,
          role: user?.role || 'individual',
        },
        customUrl: true,
      });

      if (response && response.status === true && response.data) {
        handleRoute(response, router, '', notification);
      } else {
        notification.error({
          message: 'Login Failed',
          description: response?.message || 'Please try again later.',
        });
      }
      setLoading(false);
    } catch (error) {
      setLoading(false);
      notification.error({
        message: 'Login Failed',
        description: error?.message || 'Please try again later.',
      });
    }
  };

  // Define a type guard
  const isTokenResponse = (
    response: TokenResponse | CredentialResponse
  ): response is TokenResponse => {
    return 'access_token' in response;
  };

  const isCredentialResponse = (
    response: TokenResponse | CredentialResponse
  ): response is CredentialResponse => {
    return 'credential' in response;
  };

  const handleSuccess = async (
    credentialResponse: TokenResponse | CredentialResponse,
    type: 'google' | 'apple' | 'linkedIn' | 'facebook',
    userObj?: object
  ) => {
    let token = '';
    setSocialLoading(type);

    if (isTokenResponse(credentialResponse)) {
      token = credentialResponse.access_token;
    } else if (isCredentialResponse(credentialResponse)) {
      token = credentialResponse.credential || '';
    }

    if (token) {
      const values: SocialLoginPayload = {
        token,
        type: type || 'google',
        role: user?.role || 'individual',
      };

      if (type === 'apple' && userObj && !isEmpty(userObj)) {
        values.full_name_obj = userObj;
      }

      const res = await handleSocialLogin(values);
      if (res?.data && res.status === true) {
        handleRoute(res, router);
      } else {
        notification.error({
          message: 'Login Failed',
          description: res?.message || 'Please try again later.',
        });
      }
      setSocialLoading('');
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleError = (error?: unknown) => {
    notification.error({
      message: 'Login Failed',
      description: 'Please try again later.',
    });
    // console.error('Login Failed');
  };

  const login = useGoogleLogin({
    onSuccess: tokenResponse => handleSuccess(tokenResponse, 'google'),
    onError: error => {
      handleError(error);
      console.error('Login error ==>', error);
    },
  });

  const type = user?.role;

  let btnWidth = '600px';
  if (isTablet) {
    btnWidth = '500px';
  } else if (isMobile) {
    btnWidth = '400px';
  }

  const sm = useMediaQuery('(max-width: 900px)');
  const xs = useMediaQuery('(max-width: 600px)');
  // get the domain
  const getBaseUrl = () => {
    if (typeof window !== 'undefined') {
      return window.location.origin;
    }
    return process.env.NEXT_PUBLIC_BASE_URL || '';
  };
  return (
    <AuthWrapper>
      <div className={authStyles.logoSection}>
        <Image
          src={theme === 'dark' ? images.whiteLogo : images.horizontalLogo}
          alt='Swiss Trust Layer'
          className={authStyles.logoImage}
          width={180}
          height={180}
          style={{
            width: xs ? '175px' : sm ? '190px' : '220px',
            height: 'auto',
          }}
        />

        <Title level={2} className={authStyles.formTitle}>
          {type === 'agency'
            ? t('login.agency_title')
            : `${t('login.user_title')} 👋`}
        </Title>
        <Text className={authStyles.formSubtitle}>
          {type === 'agency'
            ? t('login.agency_subtitle')
            : t('login.user_subtitle')}
        </Text>
      </div>

      {/* {error && (
        <Alert
          message={error}
          type='error'
          showIcon
          style={{ marginBottom: 16 }}
        />
      )} */}

      <Form
        form={form}
        layout='vertical'
        onFinish={handleSubmit}
        autoComplete='off'
      >
        <Form.Item
          label={
            type === 'agency' ? t('login.agency_email') : t('login.user_email')
          }
          name='email'
          rules={[
            { required: true, message: `${t('login.required_email')}` },
            { type: 'email', message: `${t('login.valid_email')}` },
          ]}
          className={authStyles.formItem}
        >
          <Input
            placeholder={
              type === 'agency'
                ? t('login.agency_email_placeholder')
                : t('login.email_placeholder')
            }
            type='email'
            className={authStyles.input}
          />
        </Form.Item>

        <Form.Item
          label={t('login.password')}
          name='password'
          rules={[
            { required: true, message: `${t('login.required_password')}` },
          ]}
          className={authStyles.formItem}
        >
          <Input.Password
            placeholder={t('login.password_placeholder')}
            className={authStyles.passwordInput}
          />
        </Form.Item>

        {/* <Form.Item
          label={t('login.agency_name')}
          name='agency_name'
          rules={[
            { required: true, message: `${t('login.agency_name_required')}` },
          ]}
          className={authStyles.formItem}
        >
          <Input.Password
            placeholder={t('login.agency_name_placeholder')}
            className={authStyles.passwordInput}
          />
        </Form.Item> */}

        <div className={authStyles.actionsRow}>
          <Form.Item
            name='rememberMe'
            valuePropName='checked'
            style={{ margin: 0 }}
          >
            <Checkbox
              checked={rememberMe}
              onChange={e => setRememberMe(e.target.checked)}
              className={authStyles.rememberMe}
            >
              {t('login.remember_me')}
            </Checkbox>
          </Form.Item>
          <div className={authStyles.forgotPasswordLink}>
            <Link href='/forgot-password'>{t('login.forgot_password')}</Link>
          </div>
        </div>

        <Form.Item style={{ marginBottom: 0 }}>
          <Button
            type='primary'
            htmlType='submit'
            loading={loader}
            className={authStyles.primaryButton}
          >
            {t('login.login')}
          </Button>
        </Form.Item>
      </Form>

      <div className={authStyles.authFooter}>
        <Text className={authStyles.footerText}>
          {t('login.dont_have_account')}{' '}
          <Link href='/signup' className={authStyles.signupLink}>
            {t('login.sign_up')}
          </Link>
        </Text>

        <Divider plain>or</Divider>

        <div className={authStyles.oauthButtons}>
          {/* <Button
            icon={<GoogleOutlined />}
            block
            className={authStyles.oauthBtn}
          >
            {t('login.continue_with_google')}
          </Button> */}
          <div style={{ width: '100%' }}>
            {/* <GoogleLogin
              onSuccess={handleSuccess}
              onError={handleError}
              containerProps={{
                style: {
                  display: 'flex',
                  width: btnWidth,
                  maxWidth: btnWidth,
                  justifyContent: 'center',
                  borderRadius: '10px',
                },
              }}
              width={btnWidth}
              type='standard'
              logo_alignment='center'
              text={'continue_with'}
              useOneTap={false}
            /> */}
          </div>
          <Button
            icon={<GoogleCircleFilled />}
            block
            loading={socialLoader === 'google'}
            className={authStyles.oauthBtn}
            onClick={() => {
              login();
            }}
          >
            {t('login.continue_with_google')}
          </Button>
          <AppleSignin
            authOptions={{
              clientId: `${process.env.NEXT_PUBLIC_APPLE_CLIENT_ID}`,
              scope: 'name email',
              redirectURI: `${getBaseUrl()}/apple-callback`,
              state: 'state',
              usePopup: true, // use redirect if false
            }}
            onSuccess={(response: AppleResponse) => {
              const idToken = response?.authorization?.id_token;
              const data = {
                credential: idToken,
              };
              handleSuccess(data, 'apple', response?.user?.name);
            }}
            onError={(error: unknown) => {
              console.error('Apple login error:', error);
            }}
            render={(props: {
              onClick: React.MouseEventHandler<HTMLElement> | undefined;
            }) => (
              <Button
                icon={<AppleFilled />}
                block
                loading={socialLoader === 'apple'}
                className={authStyles.oauthBtn}
                onClick={props.onClick}
              >
                {t('login.continue_with_apple')}
              </Button>
            )}
            uiType={'light'}
          />

          <FacebookLogin
            loader={socialLoader === 'facebook'}
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onSuccess={(response: any) => {
              handleSuccess(response, 'facebook');
            }}
          />
          <LinkedIn
            clientId={`${process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID}`}
            redirectUri={`${getBaseUrl()}/linkedin-callback`}
            onSuccess={code => {
              console.log('Authorization Code::', code);
            }}
            scope='openid profile email'
            onError={(error: unknown) => {
              handleError(error);
            }}
          >
            {({ linkedInLogin }) => (
              <Button
                onClick={linkedInLogin}
                block
                loading={socialLoader === 'linkedIn'}
                icon={<LinkedinFilled />}
              >
                {t('login.continue_with_linkedin')}
              </Button>
            )}
          </LinkedIn>
        </div>
      </div>

      <div className={authStyles.footerNote}>
        <Text>© SwissTrustLayer</Text>
      </div>
    </AuthWrapper>
  );
};

export default SimpleLoginPage;
