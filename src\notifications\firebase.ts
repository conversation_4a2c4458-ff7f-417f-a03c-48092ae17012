// Import the functions you need from the SDKs you need
import { Endpoints } from '@/config/endpoints';
import siteConfig from '@/config/site.config';
import { getApiData } from '@/helpers/ApiHelper';
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, Messaging } from 'firebase/messaging';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: 'swiss-trust-layer.firebaseapp.com',
  projectId: 'swiss-trust-layer',
  storageBucket: 'swiss-trust-layer.firebasestorage.app',
  messagingSenderId: process.env.NEXT_PUBLIC_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};
interface Response {
  status: boolean;
  message: string;
}
// Initialize Firebase
const app = initializeApp(firebaseConfig);
// Save token to backend
const saveToken = async (token: string) => {
  try {
    console.log('Saving FCM token to backend...');

    const res = await getApiData<
      {
        fcm_token: string;
      },
      Response
    >({
      url: `${siteConfig.apiUrl}${Endpoints.saveFcmToken}`,
      method: 'POST',
      data: { fcm_token: token },
      customUrl: true,
    });

    if (res?.status) {
      console.log('✅ FCM token saved successfully');
    } else {
      console.warn('⚠️ Failed to save FCM token');
    }
  } catch (error) {
    console.error('❌ Error saving token:', error);
  }
};
let messaging: Messaging | null = null;

if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
  try {
    messaging = getMessaging(app);
  } catch (err) {
    console.error('⚠️ Error initializing messaging:', err);
  }
}

export { messaging };

export const generateToken = async () => {
  if (typeof window === 'undefined' || !messaging) {
    return;
  }
  let permission = Notification.permission;
  console.log('🚀 ~ generateToken ~ permission:', permission);

  if (permission !== 'granted') {
    permission = await Notification.requestPermission();
  }

  if (permission === 'granted') {
    const fcmToken = await getToken(messaging, {
      vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
    });
    saveToken(fcmToken);
    console.log('🚀 ~ FCM Token:', fcmToken);
  } else if (permission === 'denied') {
    console.log('Notifications Blocked');

    // Optionally, you could show a guide on how to enable it for different browsers
  } else {
    console.log('Permission not granted for notifications');
  }
  return permission;
};
