/**
 * @fileoverview Dashboard header component with user greeting and navigation
 * @module components/Header/Header
 */

'use client';

import { BellOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Layout, Typography } from 'antd';
import ThemeToggle from '../ui/ThemeToggle';

import useMediaQuery from '@/hooks/useMediaQuery';
import { useTranslation } from '@/hooks/useTranslation';
import { useAppSelector } from '@/store/hooks';
import { capitalize } from 'lodash-es';
import { useRouter } from 'next/navigation';
import { JSX } from 'react';
import styles from './Header.module.css';

const { Header } = Layout;
const { Title, Text } = Typography;

/**
 * Dashboard header component that displays user greeting, subtitle, and navigation elements
 *
 * @component
 * @returns {JSX.Element} The rendered header component
 *
 * @example
 * // Basic usage in a dashboard layout
 * <HeaderCmp />
 *
 * @description
 * This component renders the main dashboard header containing:
 * - Personalized greeting with user's name
 * - Subtitle with activity overview
 * - Notification bell icon
 * - User avatar
 *
 * The component automatically retrieves user information from Redux store
 * and uses internationalization for text content.
 */
export default function HeaderCmp(): JSX.Element {
  const { user } = useAppSelector(s => s.auth);
  const { t } = useTranslation();
  const name = user?.fullname || '';
  const router = useRouter();
  const sm = useMediaQuery('(max-width: 426px)');
  return (
    <Header className={styles.dashboardHeader}>
      <div className={styles.headerLeft}>
        <Title level={4} className={styles.welcomeTitle}>
          {t('header.greeting').replace('{name}', capitalize(name))}
        </Title>
        <Text className={styles.welcomeSubtitle}>
          {t('dashboard.subtitleHeader')}
        </Text>
      </div>

      <div className={styles.headerRight}>
        {!sm && (
          <>
            <ThemeToggle size='large' />
            <Button
              type='default'
              className={styles.user}
              icon={<BellOutlined />}
            />
          </>
        )}
        <Avatar
          onClick={() => {
            router.replace('/settings');
          }}
          className={styles.user}
          icon={<UserOutlined />}
        />
      </div>
    </Header>
  );
}
