'use client';

import useMediaQuery from '@/hooks/useMediaQuery';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { Col, Row, Tag, Typography } from 'antd';

import { FolderData } from './FolderCard';

import { LoaderTypeParams } from '@/utils/CommonInterfaces';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useTranslation } from '../../hooks/useTranslation';
import { getStatusText } from '../../utils/CommonFunctions';
import { useNotification } from '../Notification/NotificationContext';
import AllFiles from './AllFiles';
import FolderView from './DraftIdea';
import styles from './MyIdeasTab.module.css';

const { Title, Text } = Typography;

const MyIdeasTab = ({
  folderDetails,
  loader,
  updateFolderDetails,
}: {
  folderDetails?: FolderData | null;
  loader?: boolean;
  updateFolderDetails: (type?: LoaderTypeParams) => void;
}) => {
  const sm = useMediaQuery('(max-width: 768px)');
  const xs = useMediaQuery('(max-width: 600px)');

  const { t } = useTranslation();

  const router = useRouter();

  const notification = useNotification();

  const tabs = [
    {
      id: 1,
      title: t('myIdeasTabs.myIdea'),
    },
    {
      id: 2,
      title: t('myIdeasTabs.allFiles'),
    },
    {
      id: 3,
      title: t('myIdeasTabs.sharing'),
    },
  ];

  const [selectedTab, setSelectedTab] = useState(tabs[0].title);

  return (
    <div className={styles.container}>
      <Row gutter={[16, 32]}>
        <Col
          sm={24}
          style={{
            display: sm ? 'flex' : 'block',
            alignItems: 'flex-start',
            justifyContent: sm ? 'center' : 'flex-start',
            width: sm ? '100%' : '50%',
          }}
        >
          {sm && (
            <ArrowLeftOutlined
              style={{
                fontSize: '20px',
                color: '#000',
                position: 'absolute',
                left: xs ? '0px' : '20px',
                marginTop: '5px',
              }}
              onClick={() => {
                router.back();
              }}
            />
          )}

          <div
            style={{
              textAlign: sm ? 'center' : 'left',
              display: 'flex',
              alignItems: 'center',
              gap: '10px',
              flexDirection: sm ? 'column' : 'row',
            }}
          >
            <Title level={1} className={styles.pageTitle}>
              {`Secure ${folderDetails?.name}`}
            </Title>

            <Tag
              style={{
                marginTop: sm ? '-15px' : 0,
                marginBottom: '12px',
              }}
              className={`${styles.statusTag} ${styles[`statusTag${folderDetails?.status === '2' ? 'InReview' : getStatusText(folderDetails?.status || '-1')}`]}`}
            >
              {getStatusText(folderDetails?.status || '-1')}
            </Tag>
          </div>
        </Col>
        <Col
          span={24}
          style={{
            marginTop: '-20px',
          }}
        >
          <div className={styles.tabContainer}>
            {tabs.map(data => (
              <div
                key={data.id}
                onClick={() => {
                  if (data.title === 'Sharing') {
                    notification.info({
                      message: 'Sharing',
                      description: 'Coming soon.',
                    });
                  } else {
                    setSelectedTab(data.title);
                  }
                }}
                className={
                  selectedTab === data?.title ? styles.activeTab : styles.tab
                }
              >
                <Text
                  className={
                    selectedTab === data?.title
                      ? styles.activeText
                      : styles.text
                  }
                >
                  {data.title}
                </Text>
              </div>
            ))}
          </div>
        </Col>
        {selectedTab === 'My Idea' ? (
          <Col span={24}>
            <FolderView
              folderDetails={folderDetails}
              loader={loader}
              updateFolderDetails={updateFolderDetails}
              viewMoreClick={(tab: string) => {
                setSelectedTab(tab);
              }}
            />
          </Col>
        ) : selectedTab === 'All Files' ? (
          <Col span={24}>
            <AllFiles folderDetails={folderDetails} />
          </Col>
        ) : (
          ''
        )}
      </Row>
    </div>
  );
};

export default MyIdeasTab;
