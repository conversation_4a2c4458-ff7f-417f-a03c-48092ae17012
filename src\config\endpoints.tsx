export const Endpoints = {
  signup: '/seal-my-idea/v1/signup',
  verifyOtp: '/seal-my-idea/v1/verify-otp',
  resendOtp: '/seal-my-idea/v1/resend-otp',
  forgotPassword: '/seal-my-idea/v1/forgot-password',
  changePassword: '/seal-my-idea/v1/change-password',
  login: '/seal-my-idea/v1/login',
  updateUserDetails: '/seal-my-idea/v1/update-user-details',
  goalList: '/seal-my-idea/v1/goal/all',
  interestList: '/seal-my-idea/v1/interest/all',
  skipScreen: '/seal-my-idea/v1/skip-screen',
  socialLogin: '/seal-my-idea/v1/social-signup',
  enable2FA: '/seal-my-idea/v1/enable-2fa',
  verify2FA: '/seal-my-idea/v1/verify-2fa',
  login2FA: '/seal-my-idea/v1/login-2fa',
  updateProfile: '/seal-my-idea/v1/users-settings/update-user-profile',
  verify2FAOtp: '/seal-my-idea/v1/verify-2fa-otp',

  // Folders
  createFolder: '/seal-my-idea/v1/folder/create',
  getFolderList: '/seal-my-idea/v1/folder/list',
  deleteFolder: '/seal-my-idea/v1/folder/delete',
  updateFolder: '/seal-my-idea/v1/folder/update',
  fileSuccess: '/seal-my-idea/v1/update-chunk-upload-info',
  getFolderById: '/seal-my-idea/v1/folder/get-folder',
  saveFolder: '/seal-my-idea/v1/folder/saved-folder',

  // Upload
  getSasToken: '/seal-my-idea/v1/get-sas-url',
  userAccountDetails: '/seal-my-idea/v1/users-settings/get-user-account/',
  updateCurrentPassword: '/seal-my-idea/v1/users-settings/change-user-password',
  verifyCertificate: '/seal-my-idea/v1/folder/sealed-document-certificate',

  // Stripe
  createCheckoutSession: '/seal-my-idea/v1/stripe/generate-stripe-link',
  checkPaymentStatus: '/seal-my-idea/v1/stripe/check-payment-status',
  getPaymentDetails: '/seal-my-idea/v1/stripe/get-sesion-data-by-session-id',

  getSealPlans: '/seal-my-idea/v1/stripe/get-plan-list',
  downloadReceipt: '/seal-my-idea/v1/stripe/download-invoice',

  // Seal apis
  sealFolder: '/seal-my-idea/v1/folder/seal',
  downloadFile: '/seal-my-idea/v1/folder/download-zip',
  sealedDocumentCertificate:
    '/seal-my-idea/v1/folder/sealed-document-certificate',
  saveFcmToken: '/seal-my-idea/v1/save-fcm-token',
};
