// Give the service worker access to Firebase Messaging.
// Note that you can only use Firebase Messaging here. Other Firebase libraries
// are not available in the service worker.
// Replace 10.13.2 with latest version of the Firebase JS SDK.
importScripts(
  'https://www.gstatic.com/firebasejs/10.13.2/firebase-app-compat.js'
);
importScripts(
  'https://www.gstatic.com/firebasejs/10.13.2/firebase-messaging-compat.js'
);

// Initialize the Firebase app in the service worker by passing in
// your app's Firebase config object.
// https://firebase.google.com/docs/web/setup#config-object
firebase.initializeApp({
  apiKey: 'AIzaSyDnQuITJw2uVkofT4O95omZviFTriflLlU',
  authDomain: 'swiss-trust-layer.firebaseapp.com',
  projectId: 'swiss-trust-layer',
  storageBucket: 'swiss-trust-layer.firebasestorage.app',
  messagingSenderId: 815385848837,
  appId: '1:815385848837:web:ba65ccff720f8e0950c06c',
  measurementId: 'G-P9PT3507MY',
});

// Retrieve an instance of Firebase Messaging so that it can handle background
// messages.
const messaging = firebase.messaging();
// Handle background messages
messaging.onMessage(payload => {
  console.log('📩 Background message received:', payload);

  const notificationTitle = payload?.notification?.title || 'Notification';
  const audio = new Audio('/notification.mp3'); // Put notification.mp3 in /public
  audio.play().catch(err => {
    console.warn(
      '🔇 Unable to play sound (autoplay blocked until user interacts):',
      err
    );
  });
  const notificationOptions = {
    body: payload?.notification?.body || '',
    icon: payload?.notification?.image || '/icon.png',
    sound: 'default',
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});
