import { NextRequest, NextResponse } from 'next/server';

// Routes accessible to all (authenticated or not)
const unrestrictedRoutes = [
  '/terms',
  '/offline',
  '/verify-certificate',
  '/firebase-messaging-sw.js',
  '/notification-permission',
];

// Routes only for guests (unauthenticated users)
const guestOnlyRoutes = [
  '/',
  '/login',
  '/forgot-password',
  '/verify-otp',
  '/new-password',
  '/check-your-inbox',
  '/two-factor-authentication',
  '/signup',
  '/steps',
  '/linkedin-callback',
  '/terms',
];

// Static assets & API
const publicAssets = [
  '/manifest.json',
  '/sw.js',
  '/workbox-',
  '/icons/',
  '/favicon',
  '/_next/',
  '/api/',
  '/verify-certificate/',
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Static assets should always be allowed
  if (publicAssets.some(asset => pathname.startsWith(asset))) {
    return NextResponse.next();
  }

  const authToken = request.cookies.get('auth-token')?.value;
  const isAuthenticated = authToken === 'authenticated';

  const isUnrestrictedRoute = unrestrictedRoutes.includes(pathname);
  const isGuestOnlyRoute = guestOnlyRoutes.includes(pathname);

  // Authenticated user trying to access guest-only routes → redirect to dashboard
  if (isAuthenticated && isGuestOnlyRoute) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Unauthenticated user trying to access protected routes → redirect to login
  if (!isAuthenticated && !isUnrestrictedRoute && !isGuestOnlyRoute) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  return NextResponse.next();
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
