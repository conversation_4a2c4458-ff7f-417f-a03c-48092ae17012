import { NextRequest, NextResponse } from 'next/server';

// Routes accessible to all (authenticated or not)
const unrestrictedRoutes = [
  '/terms',
  '/offline',
  '/verify-certificate',
  '/firebase-messaging-sw.js',
  '/notification-permission',
];

// Routes only for guests (unauthenticated users)
const guestOnlyRoutes = [
  '/',
  '/login',
  '/forgot-password',
  '/verify-otp',
  '/new-password',
  '/check-your-inbox',
  '/two-factor-authentication',
  '/signup',
  '/steps',
  '/linkedin-callback',
  '/terms',
];

// Onboarding routes that require authentication but are part of signup flow
const onboardingRoutes = [
  '/verify-otp',
  '/two-factor-authentication',
  '/steps',
  '/terms',
  '/notification-permission',
];

// Static assets & API
const publicAssets = [
  '/manifest.json',
  '/sw.js',
  '/workbox-',
  '/icons/',
  '/favicon',
  '/_next/',
  '/api/',
  '/verify-certificate/',
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Static assets should always be allowed
  if (publicAssets.some(asset => pathname.startsWith(asset))) {
    return NextResponse.next();
  }

  const authToken = request.cookies.get('auth-token')?.value;
  const userDataCookie = request.cookies.get('user-data')?.value;
  const isAuthenticated = authToken === 'authenticated';

  const isUnrestrictedRoute = unrestrictedRoutes.includes(pathname);
  const isGuestOnlyRoute = guestOnlyRoutes.includes(pathname);
  const isOnboardingRoute = onboardingRoutes.some(route =>
    pathname.startsWith(route)
  );

  // Parse user data from cookie for onboarding state checking
  let userData = null;
  if (userDataCookie) {
    try {
      userData = JSON.parse(decodeURIComponent(userDataCookie));
    } catch {
      // Invalid user data cookie, ignore
    }
  }

  // Check if user has completed onboarding
  const isOnboardingComplete =
    userData?.last_screen === 'completed' || userData?.is2FAEnabled === '0';

  // Authenticated user with completed onboarding trying to access guest-only or onboarding routes
  if (
    isAuthenticated &&
    isOnboardingComplete &&
    (isGuestOnlyRoute || isOnboardingRoute)
  ) {
    // Exception: allow access to terms page even after onboarding
    if (pathname === '/terms') {
      return NextResponse.next();
    }
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Authenticated user with incomplete onboarding trying to access protected routes
  if (
    isAuthenticated &&
    !isOnboardingComplete &&
    !isGuestOnlyRoute &&
    !isOnboardingRoute &&
    !isUnrestrictedRoute
  ) {
    // Redirect to appropriate onboarding step based on user data
    const redirectPath = getOnboardingRedirectPath(userData);
    return NextResponse.redirect(new URL(redirectPath, request.url));
  }

  // Authenticated user trying to access guest-only routes (but not onboarding routes)
  if (isAuthenticated && isGuestOnlyRoute && !isOnboardingRoute) {
    return NextResponse.redirect(new URL('/dashboard', request.url));
  }

  // Unauthenticated user trying to access protected routes
  if (!isAuthenticated && !isUnrestrictedRoute && !isGuestOnlyRoute) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  return NextResponse.next();
}

/**
 * Determines the appropriate redirect path based on user's onboarding progress
 */
function getOnboardingRedirectPath(
  userData: {
    email?: string;
    isEmailVerified?: boolean;
    is2FAEnabled?: string | boolean;
    last_screen?: string;
  } | null
): string {
  if (!userData) {
    return '/login';
  }

  // Check email verification
  if (!userData.isEmailVerified) {
    return `/verify-otp?email=${encodeURIComponent(userData.email || '')}&from=login`;
  }

  // Check 2FA setup and verification
  if (userData.is2FAEnabled === '-1') {
    return `/two-factor-authentication?email=${encodeURIComponent(userData.email || '')}`;
  }

  if (userData.is2FAEnabled === '1' && !userData.last_screen) {
    return `/verify-otp?email=${encodeURIComponent(userData.email || '')}&from=2FA`;
  }

  // Check onboarding steps based on last_screen
  if (!userData.last_screen) {
    return '/steps?step=0';
  }

  switch (userData.last_screen) {
    case 'screen_1_tell_about_us':
      return '/steps?step=1';
    case 'screen_2_goals':
      return '/steps?step=2';
    case 'screen_3_interest':
      return '/steps?step=3';
    case 'screen_4_here_about_us':
      return '/terms';
    case 'terms_acceptance':
      return '/notification-permission';
    case 'completed':
      return '/dashboard';
    default:
      return '/steps?step=0';
  }
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)',
  ],
};
