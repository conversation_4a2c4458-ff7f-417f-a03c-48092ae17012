'use client';

import { Form, type FormInstance } from 'antd';
import type { Rule } from 'antd/es/form';
import { PhoneNumberUtil } from 'google-libphonenumber';
import React from 'react';
import { isMobile } from 'react-device-detect';
import { PhoneInput } from 'react-international-phone';
import 'react-international-phone/style.css';

interface PhoneInputFieldProps {
  value: string;
  onChange: (data: {
    phone: string;
    country: string;
    countryCode: string;
    fullNumber: string;
  }) => void;
  theme: string;
  form: FormInstance;
  t: (key: string) => string;
  authStyles?: Record<string, string>;
  containerStyle?: React.CSSProperties;
  rules?: Rule[];
  defaultCountry?: string;
}

const PhoneNumberInput: React.FC<PhoneInputFieldProps> = ({
  value,
  onChange,
  theme,
  form,
  t,
  authStyles,
  containerStyle,
  rules = [],
  defaultCountry = 'us',
}) => {
  console.log('defaultCountry=====>>>>>', defaultCountry);

  // Use the provided country or fallback to 'us'
  const currentCountry =
    defaultCountry && defaultCountry.trim() !== '' ? defaultCountry : 'us';

  return (
    <Form.Item
      label={t('signup.mobile_number')}
      name='mobile_number'
      rules={[
        ...(rules || []),
        {
          validator: (_, value: string) => {
            if (!value || value.trim() === '') {
              return Promise.resolve();
            }
            try {
              const phoneUtil = PhoneNumberUtil.getInstance();
              const parsed = phoneUtil.parseAndKeepRawInput(value);
              if (phoneUtil.isValidNumber(parsed)) {
                return Promise.resolve();
              }
            } catch {
              return Promise.resolve(); // skip error during typing
            }
            return Promise.reject('Enter a valid phone number');
          },
        },
      ]}
      className={authStyles?.formItem || authStyles?.sectionLabel}
    >
      <PhoneInput
        key={`phone-input-${currentCountry}`} // Force re-render when country changes
        defaultCountry={currentCountry}
        value={value}
        onChange={(phone, meta) => {
          const phoneUtil = PhoneNumberUtil.getInstance();
          try {
            const parsed = phoneUtil.parseAndKeepRawInput(phone);
            const countryCode = parsed.getCountryCode();
            const nationalNumber =
              parsed?.getNationalNumber()?.toString() || '';
            onChange({
              phone: nationalNumber,
              country: meta.country?.iso2 || '',
              countryCode: `+${countryCode}`,
              fullNumber: phone,
            });
            form.setFieldValue('mobile_number', phone);
          } catch {
            const countryCallingCode = phone.match(/^\+(\d{1,3})/)?.[1];
            const nationalNumber = phone.replace(/^\+\d{1,3}/, '');
            onChange({
              phone: nationalNumber,
              country: meta.country?.iso2 || '',
              countryCode: `+${countryCallingCode || ''}`,
              fullNumber: phone,
            });
            form.setFieldValue('mobile_number', phone);
          }
        }}
        placeholder={t('signup.mobile_number_placeholder')}
        className={authStyles?.input}
        disableDialCodeAndPrefix={true}
        style={{ ...containerStyle, borderRadius: '12px' }}
        inputStyle={{
          width: '100%',
          height: isMobile ? 42 : 50,
          borderTopRightRadius: '10px',
          borderBottomRightRadius: '10px',
          border: 'none',
          padding: '0 12px',
          background: theme === 'dark' ? '#06164a' : '#F5F5F5',
          color: theme === 'dark' ? '#fff' : '#000',
          fontSize: isMobile ? '14px' : '16px',
          fontFamily: 'var(--font-poppins)',
          transition: 'all 0.2s ease',
        }}
        countrySelectorStyleProps={{
          buttonStyle: {
            height: '100%',
            borderTopLeftRadius: '10px',
            borderBottomLeftRadius: '10px',
            border: 'none',
            borderRight: 'none',
            background: theme === 'dark' ? '#06164a' : '#F5F5F5',
            color: theme === 'dark' ? '#fff' : '#000',
            transition: 'all 0.2s ease',
            paddingLeft: '10px',
          },
        }}
      />
    </Form.Item>
  );
};

export default PhoneNumberInput;
