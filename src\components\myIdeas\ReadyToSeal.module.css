.container {
  width: 100%;
}

.pageTitle {
  font-size: 32px !important;
  font-weight: 600 !important;
  color: var(--color-black) !important;
}

.subTitle {
  display: block;
  text-align: left;
  margin-bottom: 10px;
  color: #555555 !important;
  font-size: 18px;
  line-height: 1.4;
  font-family: var(--font-poppins);
  font-weight: 300;
  margin-top: -10px;
}

.docList {
  display: flex;
  flex-direction: column;
  gap: 3px;
  margin-top: 20px;
  width: 100%;
}

.docItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #ffff;
  padding: 8px;
  border-radius: 8px;
  cursor: pointer !important;
  transition: all 0.3s ease;
  margin-bottom: 10px;
  width: 100%;
  box-shadow: 2.36px 5.91px 30.71px rgba(0, 0, 0, 0.09);
}
:global(html[data-theme='dark']) .docItem {
  background-color: #f5f5f5;
}

.docItem:hover {
  box-shadow: 0 0 8px var(--color-secondary);
  transition: all 0.3s ease;
}

.docCardRow {
  gap: 20px;
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
}
.iconWrap {
  display: flex;
  align-items: center;
}

.label {
  font-size: 18px;
  color: #000000;
  font-weight: 500;
  font-family: var(--font-radio-canada);
  line-height: 16px;
  text-overflow: ellipsis;
}
.description {
  font-size: 12px;
  color: #555555;
  font-weight: 300;
  font-family: var(--font-poppins);
  line-height: 10px;
  margin-top: 10px;
}

.checkIcon {
  background-color: #0cd857;
  border-radius: 50%;
  padding: 5px 8px;
  font-size: 10px;
  color: white;
  font-weight: bold;
  box-shadow: 0px 10.63px 16.54px rgba(0, 0, 0, 0.14);
}

.textArea {
  background-color: #edeff1;
}

.formLabel {
  font-size: 18px;
  color: #000000;
  font-weight: 500;
  font-family: var(--font-radio-canada);
  line-height: 16px;
  margin-bottom: 12px;
  margin-top: 20px;
}

.countryList {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 10px;
  width: 100%;
}

.countryItem {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer !important;
  transition: all 0.3s ease;
  position: relative;
}

.countryItem:hover {
  transform: scale(1.05);
  transition: all 0.3s ease;
}

.countryItem.selected .flagContainer {
  border: 3px solid var(--color-primary, #1890ff);
}

.flagContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid transparent;
  transition: all 0.3s ease;
  background-color: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.countryName {
  font-size: 14px;
  color: #000000;
  font-weight: 500;
  font-family: var(--font-radio-canada);
  text-align: center;
}

.btnParent {
  display: flex;
  justify-content: center;
  width: 100%;
}

.buttonGroup {
  display: flex;
  justify-content: center;
  flex-direction: column;
  margin-top: 20px;
  gap: 12px;
  width: 450px;
}

.priceContainer {
  /* width: 100%; */
  text-align: end;
}

.priceText {
  color: #555555 !important;
  font-family: var(--font-radio-canada);
  font-weight: 400;
  font-size: 16px;
}

.price {
  color: #012458 !important;
  font-family: var(--font-radio-canada);
  font-weight: 600;
  font-size: 55px;
}
:global(html[data-theme='dark']) .price {
  color: #fff !important;
}

:global(html[data-theme='dark']) .priceText {
  color: #edeff1 !important;
}

.viewMoreButton {
  font-size: 18px !important;
  font-weight: 500 !important;
  color: #012458 !important;
  font-family: var(--font-radio-canada) !important;
  padding: 8px 16px !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
  text-decoration: none !important;
}

.viewMoreButton :hover {
  text-decoration: underline !important;
}

:global(html[data-theme='dark']) .docItem {
  background-color: #16406f;
}

:global(html[data-theme='dark']) .pageTitle {
  color: #fff !important;
}
:global(html[data-theme='dark']) .subTitle {
  color: #edeff1 !important;
}
:global(html[data-theme='dark']) .label {
  color: #fff !important;
}
:global(html[data-theme='dark']) .description {
  color: #edeff1 !important;
}
:global(html[data-theme='dark']) .textArea {
  background-color: #06164a;
}
:global(html[data-theme='dark']) .formLabel {
  color: #fff !important;
}
:global(html[data-theme='dark']) .countryItem {
  background-color: #16406f;
}
:global(html[data-theme='dark']) .countryName {
  color: #fff !important;
}

@media (max-width: 1200px) {
  .pageTitle {
    font-size: 32px !important;
  }
  .subTitle {
    font-size: 16px !important;
  }
  .formLabel {
    font-size: 16px !important;
  }
}

@media (max-width: 900px) {
  .pageTitle {
    font-size: 32px !important;
  }
  .subTitle {
    font-size: 14px !important;
  }
  .label {
    font-size: 16px;
  }
  .formLabel {
    font-size: 16px !important;
  }
  .description {
    font-size: 12px !important;
  }

  .priceText {
    font-size: 14px;
  }
  .price {
    font-size: 40px;
  }
  .viewMoreButton {
    font-size: 16px !important;
    margin-bottom: -10px;
  }
  .textArea {
    margin-top: -10px;
  }
}

@media (max-width: 625px) {
  .container {
    padding: 12px;
  }
}

@media (max-width: 600px) {
  .pageTitle {
    font-size: 24px !important;
  }
  .formLabel {
    font-size: 14px !important;
  }
  .priceText {
    font-size: 14px;
  }
  .price {
    font-size: 30px;
  }

  .label {
    font-size: 14px;
  }

  .viewMoreButton {
    font-size: 14px !important;
  }
}

@media (max-width: 480px) {
  .pageTitle {
    font-size: 22px !important;
  }
  .subTitle {
    font-size: 12px !important;
  }

  .priceText {
    font-size: 14px;
  }
  .price {
    font-size: 26px;
  }

  .flagContainer {
    width: 40px;
    height: 40px;
  }
  .countryList {
    gap: 10px;
  }
}
